import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/material.dart';
import 'package:ms_http/ms_http.dart';

import '../app/repository/http/interceptor/cache_interceptor.dart';
import '../app/repository/http/interceptor/error_interceptor.dart';
import '../app/repository/http/interceptor/http_log_interceptor.dart';
import '../app/repository/http/interceptor/request_interceptor.dart';
import '../app/repository/http/interceptor/signature_interceptor.dart';
import '../app/repository/http/interceptor/token_interceptor.dart';
import '../app/repository/modals/account/account.dart';
import '../utils/prefs_util.dart';
import 'config.dart';
import 'constant.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: global_config
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:16
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:16
/// @UpdateRemark: 更新说明
class GlobalConfig {
  /// 是否是release版
  static bool get isRelease => const bool.fromEnvironment("dart.vm.product");

  /// 用户信息
  static Account? account;

  /// 当前语言环境
  static Locale? localeInfo;

  /// 用户协议
  static bool? agreePrivacy;

  /// 当前路由
  static String? currentRouter;

  /// 是否开启代理(打包时选择不同配置关闭或者开启代理)
  static bool enableProxy = Config.vm;

  /// baseUrl
  static String baseUrl = "";

  /// 初始化全局信息，会在APP启动时执行
  static Future init() async {
    /// 初始化本地存储
    await PrefsUtil().init();

    /// 初始化网络请求相关配置
    if (enableProxy) {
      var isTest = PrefsUtil().getBool(PrefsKeys.serverWitchKey);
      baseUrl = isTest == false ? Constant.releaseBaseUrl : Constant.devBaseUrl;
    } else {
      baseUrl = Constant.releaseBaseUrl;
    }

    // 添加拦截器
    List<Interceptor> interceptors = [
      RequestInterceptor(),
      SignatureInterceptor(),
      TokenInterceptor(),
      ErrorInterceptor(),
      NetCacheInterceptor(),
      HttpLogInterceptor()
    ];

    HttpUtils.init(
      baseUrl: baseUrl,
      interceptors: interceptors,
      httpClientAdapter: _buildProxy(),
    );

    /// 获取用户信息
    var userInfo = PrefsUtil().getJSON("account");
    if (userInfo != null) {
      account = Account.fromJson(userInfo);
    }

    /// 用户是否同意协议
    agreePrivacy = PrefsUtil().getBool(PrefsKeys.agreePrivacy);
  }

  /// 持久化用户信息
  static saveProfile() => PrefsUtil().setJSON("account", account?.toJson());

  /// 代理设置，一般用于测试抓包设置
  static HttpClientAdapter? _buildProxy() {
    if (!enableProxy) return null;
    var ip = PrefsUtil().getString(PrefsKeys.proxyIpKey);
    var port = PrefsUtil().getString(PrefsKeys.proxyPortKey);
    if (ip != null && ip.isNotEmpty && port != null && port.isNotEmpty) {
      var adapter = IOHttpClientAdapter(
        createHttpClient: () {
          final HttpClient client = HttpClient();
          client.findProxy = (uri) {
            // Proxy all request to localhost:8888.
            // Be aware, the proxy should went through you running device,
            // not the host platform.
            return 'PROXY $ip:$port';
          };
          // You can test the intermediate / root cert here. We just ignore it.
          client.badCertificateCallback = (cert, host, port) => true;
          return client;
        },
      );
      return adapter;
    }
    return null;
  }
}
