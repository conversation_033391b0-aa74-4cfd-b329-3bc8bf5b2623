import 'package:flutter/material.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package: 
/// @ClassName: theme_config
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:15
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:15
/// @UpdateRemark: 更新说明

/// light theme
const MaterialColor lightColor = MaterialColor(_lightColorPrimaryValue, <int, Color>{
  50: Color(0xFFFFFFFF),
  100: Color(0xFFFFFFFF),
  200: Color(0xFFFFFFFF),
  300: Color(0xFFFFFFFF),
  400: Color(0xFFFFFFFF),
  500: Color(_lightColorPrimaryValue),
  600: Color(0xFFFFFFFF),
  700: Color(0xFFFFFFFF),
  800: Color(0xFFFFFFFF),
  900: Color(0xFFFFFFFF),
});
const int _lightColorPrimaryValue = 0xFFFFFFFF;

const MaterialColor lightColorAccent = MaterialColor(_lightColorAccentValue, <int, Color>{
  100: Color(0xFFFFFFFF),
  200: Color(_lightColorAccentValue),
  400: Color(0xFFFFFFFF),
  700: Color(0xFFFFFFFF),
});
const int _lightColorAccentValue = 0xFFFFFFFF;
/// light theme

/// dark theme
const MaterialColor darkColor = MaterialColor(_darkColorPrimaryValue, <int, Color>{
  50: Color(0xFFE0E0E0),
  100: Color(0xFFB3B3B3),
  200: Color(0xFF808080),
  300: Color(0xFF4D4D4D),
  400: Color(0xFF262626),
  500: Color(_darkColorPrimaryValue),
  600: Color(0xFF000000),
  700: Color(0xFF000000),
  800: Color(0xFF000000),
  900: Color(0xFF000000),
});
const int _darkColorPrimaryValue = 0xFF000000;

const MaterialColor darkColorAccent = MaterialColor(_darkColorAccentValue, <int, Color>{
  100: Color(0xFFA6A6A6),
  200: Color(_darkColorAccentValue),
  400: Color(0xFF737373),
  700: Color(0xFF666666),
});
const int _darkColorAccentValue = 0xFF8C8C8C;
/// dark theme