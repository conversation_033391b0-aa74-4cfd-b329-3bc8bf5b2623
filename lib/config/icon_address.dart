/// ===assets img========
/// tab
const digitalHuman = "assets/images/digital_human.png";
const digitalHumanActive = "assets/images/digital_human_active_icon.png";
const aiTool = "assets/images/ai_tool_icon.png";
const aiToolActive = "assets/images/ai_tool_active_icon.png";
const me = "assets/images/me.png";
const meActive = "assets/images/me_active_icon.png";

/// me widget
const membershipBg = "assets/images/me_membership_bg.png";
const membershipBag = "assets/images/me_membership_bag.png";
const memberVipBag = "assets/images/me_membervip_bag.png";
const managerIcon = "assets/images/manager_icon.png";
const refreshIcon = "assets/images/refresh_icon.png";
const avatar = "assets/images/avatar.png";

/// creation
const inputIcon = "assets/images/input_icon.png";
const timbreIcon = "assets/images/timbre_icon.png";
const creationIcon = "assets/images/creation_icon.png";
const anchorIcon = "assets/images/anchor_icon.png";
const anchorVoiceIcon = "assets/images/anchor_voice_icon.png";
const listenIcon = "assets/images/listen_icon.png";
const soundIcon = "assets/images/sound_icon.png";
const previousIcon = "assets/images/previous_icon.png";
const tutorialIcon = "assets/images/tutorial_icon.png";
const descIcon = "assets/images/desc_icon.png";
const videoStyleSelect = "assets/images/video_style_select.png";
const creationProcess = "assets/images/creation_process.png";
const creationEmpty = "assets/images/creation_empty.png";
const creationFail = "assets/images/creation_fail.png";

/// preview
const playIcon = "assets/images/play_icon.png";
const pauseIcon = "assets/images/pause_icon.png";

/// about
const aboutUsIcon = "assets/images/about_us_icon.png";
const aboutUsIconAndroid = "assets/images/about_us_icon_android.png";

/// base
const arrowLeft = "assets/images/arrow_left.png";
const delete = "assets/images/delete.png";
const setting = "assets/images/setting_icon.png";
const cancelAccount = "assets/images/cancel_account_icon.png";
const sdkInfoIcon = "assets/images/sdk_info_icon.png";
const userInfoIcon = "assets/images/user_info_icon.png";
const creationResult = "assets/images/creation_result.png";
const creationSuccessShowing = "assets/images/video_creation_success.jpeg";
const continuousUpdatesIcon = "assets/images/continuous_updates.png";
const fullFeaturesIcon = "assets/images/full_features.png";
const memberLogoIcon = "assets/images/member_logo.png";
const multiTimbralIcon = "assets/images/multi_timbral.png";
const descBg = "assets/images/desc_bg.png";
const voiceLogo = "assets/images/voice_logo.png";
const powerIcon = "assets/images/power_icon.png";
const powerBg = "assets/images/power_bg.png";
const chatDelete = "assets/images/chat_delete.png";
const chatMore = "assets/images/chat_more.png";
const chatSend = "assets/images/chat_send.png";
const chatAvatar = "assets/images/chat_avatar.png";
const chatCopy = "assets/images/chat_copy.png";
const chatRechat = "assets/images/chat_rechat.png";
const chatHeader = "assets/images/chat_header.png";
const uploadCamera = "assets/images/upload_camera.png";
const uploadFile = "assets/images/upload_file.png";
const uploadPhoto = "assets/images/upload_photo.png";
const uploadFileIcon = "assets/images/upload_file_icon.png";
const recordDownIcon = "assets/images/record_down_icon.png";
const powerGreyIcon = "assets/images/power_grey_icon.png";
const digitalHumanBg = "assets/images/digital_human_bg.png";
const digitalAddIcon = "assets/images/digital_add_icon.png";
const customizationDigitalIcon = "assets/images/customization_digital_icon.png";
const customizationVoiceIcon = "assets/images/customization_voice_icon.png";
const digitalPublicHeaderIcon = "assets/images/digital_public_header_icon.png";
const makeVideoIcon = "assets/images/make_video_icon.png";
const uploadVideoIcon = "assets/images/upload_video_icon.png";
const faceVideoIcon = "assets/images/face_video_icon.png";
const myProductIcon = "assets/images/my_product_icon.png";
const randomContentIcon = "assets/images/random_content_icon.png";
const digitalPreviewIcon = "assets/images/digital_preview_icon.png";
const concatUsIcon = "assets/images/concat_us_icon.png";
const meAboutIcon = "assets/images/me_about_icon.png";
const powerDetailIcon = "assets/images/power_detail_icon.png";
const meRefreshBtn = "assets/images/me_refresh_btn.png";
const videoFailIcon = "assets/images/video_fail_icon.png";
const videoProgressIcon = "assets/images/video_progress_icon.png";
const videoMoreIcon = "assets/images/video_more_icon.png";
const videoBgFail = "assets/images/video_bg_fail.png";

/// glide
const tabAiChat = "assets/images/tab_ai_chat.gif";
const glideHome = "assets/images/glide_home.webp";
const glideHomeActive = "assets/images/glide_home_active.webp";
const glideSame = "assets/images/glide_same.webp";
const glideSameActive = "assets/images/glide_same_active.webp";
const glideProduct = "assets/images/glide_product.webp";
const glideProductActive = "assets/images/glide_product_active.webp";
const glideMe = "assets/images/glide_me.webp";
const glideMeActive = "assets/images/glide_me_active.webp";
const glideMeHeaderBg = "assets/images/glide_me_header_bg.webp";
const textToVideoCreativeIcon = "assets/images/text_to_video_creative_icon.webp";
const textToVideoParameterIcon = "assets/images/text_to_video_parameter_icon.webp";
const textToVideoCaseIcon = "assets/images/text_to_video_case_icon.webp";
const consumingInspirationIcon = "assets/images/consuming_inspiration_icon.webp";

/// restoration
const photoCompareIcon = "assets/images/restoration/photo_compare_icon.webp";
const oldPhotoRestorationBg = "assets/images/restoration/old_photo_restoration_bg.webp";
const oldPhotoDemo = "assets/images/restoration/old_photo_demo.webp";
const oldPhotoRestorationDemo = "assets/images/restoration/old_photo_restoration_demo.webp";
const restorationAfterTip = "assets/images/restoration/restoration_after_tip.webp";
const restorationBeforeTip = "assets/images/restoration/restoration_before_tip.webp";
const photoRestorationHeader = "assets/images/restoration/photo_restoration_header.webp";
const qualityPhotoDemo = "assets/images/restoration/quality_photo_demo.webp";
const qualityPhotoRestorationDemo = "assets/images/restoration/quality_restoration_demo.webp";
const qualityRestorationHeader = "assets/images/restoration/quality_restoration_header.webp";
const qualityPhotoRestorationBg = "assets/images/restoration/quality_photo_restoration_bg.webp";
const mattingDemo = "assets/images/restoration/matting_demo.webp";
const mattingDemoBg = "assets/images/restoration/matting_demo_bg.webp";
const mattingTransparentBg = "assets/images/restoration/matting_transparent_bg.webp";
const modificationBg = "assets/images/restoration/modification_bg.webp";
const modificationSelectIcon = "assets/images/restoration/modification_select_icon.webp";
const uploadLoading = "assets/images/restoration/upload_loading.webp";
const redoIcon = "assets/images/restoration/redo_icon.webp";
const undoIcon = "assets/images/restoration/undo_icon.webp";
const photoPortraitIdPhoto = "assets/images/restoration/photo_portrait_id_photo.webp";
const photoPortraitDeleteIcon = "assets/images/restoration/photo_portrait_delete_icon.webp";

/// audio
const correctExamIcon = "assets/images/correct_exam_icon.webp";
const errorExamIcon = "assets/images/error_exam_icon.webp";
const errorExamWhiteIcon = "assets/images/error_exam_white_icon.webp";
const audioManageIcon = "assets/images/audio_manage_icon.webp";
const recoedAudioIcon = "assets/images/recoed_audio_icon.webp";

/// power
const powerBgNew = "assets/images/power/power_bg.webp";
const powerTag = "assets/images/power/power_tag.webp";
const powerTagBg = "assets/images/power/power_tag_bg.webp";
const contactIcon = "assets/images/power/contact_icon.webp";
const inspirationIcon = "assets/images/power/inspiration_icon.webp";
const myWorkIcon = "assets/images/power/my_work_icon.webp";
const settingIcon = "assets/images/power/setting_icon.webp";
const powerValueIcon = "assets/images/power/power_value_icon.webp";
const memberTag = "assets/images/power/member_tag.webp";
const memberInterestsHead = "assets/images/power/member_interests_head.webp";