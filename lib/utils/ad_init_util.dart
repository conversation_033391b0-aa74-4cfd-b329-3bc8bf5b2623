/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: utils
/// @ClassName: ad_init_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/17 14:13
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/17 14:13
/// @UpdateRemark: 更新说明
library;

// import 'package:anythink_sdk/at_index.dart';
// import 'package:flutter/material.dart';

final adInitManger = InitTool();

class InitTool {
  // // 打开SDK的Debug log，强烈建议在测试阶段打开，方便排查问题。
  // setLogEnabled() async {
  //   await ATInitManger.setLogEnabled(
  //     logEnabled: true,
  //   ).then((value) {
  //     debugPrint('Set log switch $value');
  //   });
  // }
  //
  // // 设置渠道，可用于统计数据和进行流量分组
  // setChannelStr() async {
  //   await ATInitManger.setChannelStr(
  //     channelStr: "test_setChannel",
  //   ).then((value) {
  //     debugPrint('Set up channels $value');
  //   });
  // }
  //
  // // 设置子渠道，可用于统计数据和进行流量分组
  // setSubchannelStr() async {
  //   await ATInitManger.setSubChannelStr(
  //     subchannelStr: "test_setSubchannelStr",
  //   ).then((value) {
  //     debugPrint('Set up sub-channels');
  //   });
  // }
  //
  // // 设置自定义的Map信息，可匹配后台配置的对应流量分组（App纬度）（可选配置）
  // setCustomDataDic() async {
  //   await ATInitManger.setCustomDataMap(
  //     customDataMap: {
  //       'setCustomDataDic': 'myCustomDataDic',
  //     },
  //   ).then((value) {
  //     debugPrint('Set up custom rules');
  //   });
  // }
  //
  // // 设置排除交叉推广App列表
  // setExludeBundleIDArray() async {
  //   await ATInitManger.setExludeBundleIDArray(
  //     exludeBundleIDList: ['test_setExludeBundleIDArray'],
  //   ).then((value) {
  //     debugPrint('Set up exclusion of cross-promotion');
  //   });
  // }
  //
  // // 设置自定义的Map信息，可匹配后台配置的对应的流量分组（Placement纬度）（可选配置）
  // setPlacementCustomData() async {
  //   await ATInitManger.setPlacementCustomData(
  //     placementIDStr: 'b5b72b21184aa8',
  //     placementCustomDataMap: {
  //       'setPlacementCustomData': 'test_setPlacementCustomData'
  //     },
  //   ).then((value) {
  //     debugPrint('Set pl rules');
  //   });
  // }
  //
  // // 判断是否位于欧盟地区
  // getUserLocation() async {
  //   await ATInitManger.getUserLocation().then((value) {
  //     debugPrint('flutter: Get user location -- ${value.toString()}');
  //   });
  // }
  //
  // // 获取GDPR的授权级别
  // getGDPRLevel() async {
  //   await ATInitManger.getGDPRLevel().then((value) {
  //     debugPrint('flutter:Get GDPR --${value.toString()}');
  //   });
  // }
  //
  // setDataConsentSet() async {
  //   await ATInitManger.setDataConsentSet(
  //     gdprLevel: ATInitManger.dataConsentSetPersonalized(),
  //   ).then((value) {
  //     debugPrint('flutter: Set up GDPR${value.toString()}');
  //   });
  // }
  //
  // deniedUploadDeviceInfo() async {
  //   await ATInitManger.deniedUploadDeviceInfo(
  //     deniedUploadDeviceInfoList: [ATInitManger.aOAID()],
  //   ).then((value) {
  //     debugPrint('flutter: End of initialization');
  //   });
  // }
  //
  // // 初始化TopOn的SDK
  // initTopon() async {
  //   await ATInitManger.initAnyThinkSDK(
  //     appidStr: "Configuration.appidStr",
  //     appidkeyStr: "Configuration.appidkeyStr",
  //   );
  // }
}
