/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: string_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/7 11:11
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/7 11:11
/// @UpdateRemark: 更新说明
extension ExtString on String {
  /// 邮箱脱敏
  String get emailDesensitization {
    int at = indexOf("@");
    String sub;
    String suffix;
    if (at <= 3) {
      sub = substring(0, at);
      suffix = substring(at, length);
    } else {
      sub = substring(0, 3);
      suffix = substring(at, length);
    }
    return "$sub***$suffix";
  }

  /// 手机号
  String get phoneDesensitization {
    if (length <= 3) {
      return "$this****";
    }
    if (length <= 7) {
      String sub = substring(0, 3);
      return "$sub****";
    }
    String sub = substring(0, 3);
    String suffix = substring(length - 4, length);
    return "$sub****$suffix";
  }
}
