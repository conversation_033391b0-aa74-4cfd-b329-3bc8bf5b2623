/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: signature_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/22 16:51
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/22 16:51
/// @UpdateRemark: 字符加密、正则匹配等文本处理
class SignatureUtil {
  static bool phoneNumMatch(String phone) {
    RegExp exp = RegExp(
        r'^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\d{8}$');
    return exp.hasMatch(phone);
  }
}
