import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../app/navigation/router.dart';
import '../config/global_config.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: login_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/20 16:25
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/20 16:25
/// @UpdateRemark: 更新说明
class RouterUtil {
  RouterUtil._internal();

  static final RouterUtil _instance = RouterUtil._internal();

  factory RouterUtil() {
    return _instance;
  }

  /// 检查登录状态,未登录跳转登录页
  /// call: 需要登录才能执行的方法
  static void checkLogin(
    BuildContext context, {
    Function? call,
  }) async {
    if (GlobalConfig.account != null) {
      call?.call();
    } else {
      /// 默认登录页
      context.push('/$login');
    }
  }
}
