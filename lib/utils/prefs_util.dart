import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: prefs_util.dart
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:17
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:17
/// @UpdateRemark: 更新说明

class PrefsKeys {
  /// 存储选择的语言
  static const String localeLocal = "localeLocal";

  /// 存储选择的主题色
  static const String themeLocal = "themeLocal";

  /// 存储用户是否同意协议
  static const String agreePrivacy = "agreePrivacy";

  /// 存储代理IP key
  static const String proxyIpKey = "proxyIpKey";

  /// 存储代理port key
  static const String proxyPortKey = "proxyPortKey";

  /// 服务器切换key
  static const String serverWitchKey = "serverWitchKey";

  /// 存储音频列表的键名
  static const String audioListKey = "audio_list_storage";
}

/// 本地存储
class PrefsUtil {
  PrefsUtil._internal();

  static final PrefsUtil _instance = PrefsUtil._internal();

  factory PrefsUtil() {
    return _instance;
  }

  SharedPreferences? prefs;

  Future<void> init() async {
    prefs = await SharedPreferences.getInstance();
  }

  Future<bool>? setJSON(String key, dynamic jsonVal) {
    String jsonString = jsonEncode(jsonVal);
    return prefs?.setString(key, jsonString);
  }

  dynamic getJSON(String key) {
    String? jsonString = prefs?.getString(key);
    return jsonString == null ? null : jsonDecode(jsonString);
  }

  Future<bool>? setBool(String key, bool val) {
    return prefs?.setBool(key, val);
  }

  bool? getBool(String key) {
    return prefs?.getBool(key);
  }

  Future<bool>? setInt(String key, int val) {
    return prefs?.setInt(key, val);
  }

  int? getInt(String key) {
    return prefs?.getInt(key);
  }

  Future<bool>? setString(String key, String val) {
    return prefs?.setString(key, val);
  }

  String? getString(String key) {
    return prefs?.getString(key);
  }

  Future<bool>? remove(String key) {
    return prefs?.remove(key);
  }
}
