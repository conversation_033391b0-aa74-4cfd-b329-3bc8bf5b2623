import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ui_widgets/ui_widgets.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: toast_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/30 17:02
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/30 17:02
/// @UpdateRemark: Toast
class ToastUtil {
  /// 显示自定义Toast
  static showToast(String msg, {bool state = false}) {
    SmartDialog.showToast(
      "",
      builder: (context) {
        return CustomToast(
          msg: msg,
          stateWidget: state,
        );
      },
      alignment: Alignment.center,
    );
  }

  /// 显示错误自定义Toast
  static showErrorToast(String msg) {
    SmartDialog.showToast(
      "",
      builder: (context) {
        return CustomToast(msg: msg, color: Colors.red);
      },
      alignment: Alignment.center,
    );
  }
}
