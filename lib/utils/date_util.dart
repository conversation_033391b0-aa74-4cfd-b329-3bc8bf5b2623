/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: utils
/// @ClassName: date_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/10 16:54
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/10 16:54
/// @UpdateRemark: 更新说明
library;

class DateUtil {
  /// duration转成秒
  static double durationToSeconds(Duration duration) {
    return duration.inSeconds.toDouble();
  }

  /// 秒转成duration
  static Duration secondsToDuration(double seconds) {
    return Duration(seconds: seconds.toInt());
  }

  /// duration转成分秒显示
  static String durationToMinSec(Duration duration) {
    int minutes = duration.inMinutes;
    int seconds = duration.inSeconds;
    String minutesStr = minutes < 10 ? "0$minutes" : "$minutes";
    String secondsStr = seconds < 10 ? "0$seconds" : "$seconds";
    return "$minutesStr:$secondsStr";
  }
}
