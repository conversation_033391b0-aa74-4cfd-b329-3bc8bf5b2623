import 'package:flutter/material.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: trip
/// @Package:
/// @ClassName: antd_icons
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/8/24 12:00
/// @UpdateUser: frankylee
/// @UpdateData: 2022/8/24 12:00
/// @UpdateRemark: 更新说明
class VMIcons {
  static const IconData cancel = IconData(
    0xe601,
    fontFamily: 'AntdIcons',
  );
  static const IconData back = IconData(
    0xe602,
    fontFamily: 'AntdIcons',
  );
  static const IconData arrowRight = IconData(
    0xe603,
    fontFamily: 'AntdIcons',
  );
  static const IconData cancelFill = IconData(
    0xe604,
    fontFamily: 'AntdIcons',
  );
  static const IconData cancelOutline = IconData(
    0xe605,
    fontFamily: 'AntdIcons',
  );
  static const IconData exchange = IconData(
    0xe606,
    fontFamily: 'AntdIcons',
  );
  static const IconData extract = IconData(
    0xe607,
    fontFamily: 'AntdIcons',
  );
  static const IconData coinage = IconData(
    0xe608,
    fontFamily: 'AntdIcons',
  );
  static const IconData scan = IconData(
    0xe609,
    fontFamily: 'AntdIcons',
  );

  static const IconData money = IconData(
    0xe613,
    fontFamily: 'AntdIcons',
  );
}
