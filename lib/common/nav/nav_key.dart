/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: nav_key
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/7 15:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/7 15:49
/// @UpdateRemark: 更新说明
enum NavKey {
  loginSuccess,
  registerSuccess,
}

extension NavKeyExtension on NavKey {
  ///扩展方法，为枚举的value方法
  /// this会作为扩展的方法参数传递，此处的index，是this.index的简写
  String get value => [
        'loginSuccess',
        'registerSuccess',
      ][index];
}
