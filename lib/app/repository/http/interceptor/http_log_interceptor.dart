import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: log_interceptor
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/1/11 15:57
/// @UpdateUser: frankylee
/// @UpdateData: 2024/1/11 15:57
/// @UpdateRemark: 更新说明
class HttpLogInterceptor extends Interceptor {
  static const _maxLineLength = 800; // 控制日志最大行长，避免debugPrint被截断
  static const _maxLogLength = 1000; // 超过不打印

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _printLine('📤 [Request] →→→→→→→→→→→→→→→');
    _printKeyValue('URL', '${options.baseUrl}${options.path}');
    _printKeyValue('Method', options.method);
    _printKeyValue('Headers', jsonEncode(options.headers));
    _printKeyValue('Query', jsonEncode(options.queryParameters));

    if (options.data != null) {
      if (options.data is FormData) {
        final formData = options.data as FormData;
        final fields = <String, dynamic>{};
        for (var field in formData.fields) {
          fields[field.key] = field.value;
        }
        for (var file in formData.files) {
          fields[file.key] = 'File: ${file.value.filename ?? "unknown"}';
        }
        _printKeyValue('Body (FormData)', jsonEncode(fields));
      } else {
        _printKeyValue('Body', _formatJson(options.data));
      }
    }

    _printLine('📤 [Request End] ←←←←←←←←←←←←←←←');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _printLine('📥 [Response] →→→→→→→→→→→→→→→');
    _printKeyValue('URL', '${response.realUri}');
    _printKeyValue('Status Code', response.statusCode);
    _printKeyValue('Headers', jsonEncode(response.headers.map));

    final prettyJson = _formatJson(response.data);
    if (prettyJson.length > _maxLogLength) {
      _printKeyValue('Data', '📦 日志过长，请在 Flutter DevTools ➜ Network 面板查看完整内容');
    } else {
      _printLongText('Data', prettyJson);
    }

    _printLine('📥 [Response End] ←←←←←←←←←←←←←←←');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _printLine('❌ [Error] →→→→→→→→→→→→→→→');
    _printKeyValue('URL', '${err.requestOptions.uri}');
    _printKeyValue('Message', err.message);
    _printKeyValue('Stack', err.stackTrace.toString());
    _printKeyValue('Response', err.response?.data?.toString() ?? 'null');
    _printLine('❌ [Error End] ←←←←←←←←←←←←←←←');
    super.onError(err, handler);
  }

  /// 自动分页打印长文本
  void _printKeyValue(String key, dynamic value) {
    final line = '$key: $value';
    if (line.length <= _maxLineLength) {
      debugPrint(line);
    } else {
      _printLongText(key, value.toString());
    }
  }

  void _printLine(String msg) {
    debugPrint('\n$msg');
  }

  void _printLongText(String prefix, String text) {
    const chunkSize = _maxLineLength;
    final pattern = RegExp('.{1,$chunkSize}', dotAll: true);
    debugPrint('\n$prefix >>>>>>>>>>>>>>>>>>>');
    for (final match in pattern.allMatches(text)) {
      debugPrint(match.group(0));
    }
    debugPrint('<<<<<<<<<<<<<<<<<<<<<< $prefix\n');
  }

  String _formatJson(dynamic data) {
    try {
      if (data is Map || data is List) {
        return const JsonEncoder.withIndent('  ').convert(data);
      }
      return data.toString();
    } catch (e) {
      return data.toString();
    }
  }
}
