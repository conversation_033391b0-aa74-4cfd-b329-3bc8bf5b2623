import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../config/global_config.dart';

/// 利用请求拦截器在options的headers中加入token
class RequestInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    var info = {"Content-Type": "application/json"};

    // String? accessToken =
    //     "eyJhbGciOiJIUzUxMiJ9.************************************************************************************.mSzzQxFrGvIXa1XrhZcAHlXzesN29_skY-qkmQjQLInkWfDD6xk9eADNh0OqbH28r5WtFpvZPoZVT7vLTJtdwQ";
    // if (accessToken != null) {
    //   info["Authorization"] = "Bearer $accessToken";
    // }
    String? accessToken = GlobalConfig.account?.token;
    if (accessToken != null) {
      info["Authorization"] =
          "${GlobalConfig.account?.tokenPrefix} $accessToken";
    }
    Locale? locale = GlobalConfig.localeInfo;
    if (locale != null) {
      if (locale.scriptCode != null) {
        info["Locale"] = "${locale.languageCode}_${locale.scriptCode}";
      } else {
        if (locale.languageCode == "zh") {
          info["Locale"] = "${locale.languageCode}_CN";
        } else if (locale.languageCode == "en") {
          info["Locale"] = "${locale.languageCode}_US";
        } else {
          info["Locale"] = locale.languageCode;
        }
      }
    }
    options.headers.addAll(info);
    super.onRequest(options, handler);
  }
}
