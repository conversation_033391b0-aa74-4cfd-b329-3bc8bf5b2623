import 'package:json_annotation/json_annotation.dart';

part 'customize_video.g.dart';

@JsonSerializable()
class CustomizeVideo {
  String? createTime;
  int? duration;
  String? failMsg;
  int? id;
  String? previewUrl;
  int? progress;
  int? state;
  String? subtitleDataUrl;
  String? updateTime;
  int? userId;
  String? videoAlias;
  String? videoData;
  String? videoId;
  int? videoState;
  String? videoUrl;

  CustomizeVideo();

  factory CustomizeVideo.fromJson(Map<String, dynamic> json) =>
      _$CustomizeVideoFromJson(json);

  Map<String, dynamic> toJson() => _$CustomizeVideoToJson(this);
}

@JsonSerializable()
class CustomizeVideoList {
  bool? hasNextPage;
  List<CustomizeVideo>? list;
  int? total;

  CustomizeVideoList();

  factory CustomizeVideoList.fromJson(Map<String, dynamic> json) =>
      _$CustomizeVideoListFromJson(json);

  Map<String, dynamic> toJson() => _$CustomizeVideoListToJson(this);
}