import 'package:json_annotation/json_annotation.dart';

part 'public_digital_filter.g.dart';

@JsonSerializable()
class PublicDigitalFilter {
  List<String?>? genderList;
  Map<String, dynamic>? personTypeMap;
  List<String?>? audioNameList;

  PublicDigitalFilter();

  factory PublicDigitalFilter.fromJson(Map<String, dynamic> json) =>
      _$PublicDigitalFilterFromJson(json);

  Map<String, dynamic> toJson() => _$PublicDigitalFilterToJson(this);
}