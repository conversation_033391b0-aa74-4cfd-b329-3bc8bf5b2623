import 'package:json_annotation/json_annotation.dart';

part 'public_digital_human.g.dart';

@JsonSerializable()
class PublicDigitalHuman {
  String? audioId;
  String? audioName;
  String? audioPreview;
  String? cover;
  String? createTime;
  String? gender;
  int? height;
  int? id;
  String? personId;
  String? personName;
  String? personType;
  String? previewVideoUrl;
  int? state;
  String? updateTime;
  int? width;

  PublicDigitalHuman();

  factory PublicDigitalHuman.fromJson(Map<String, dynamic> json) =>
      _$PublicDigitalHumanFromJson(json);

  Map<String, dynamic> toJson() => _$PublicDigitalHumanToJson(this);
}

@JsonSerializable()
class PublicDigitalHumanList {
  bool? hasNextPage;
  List<PublicDigitalHuman>? list;
  int? total;

  PublicDigitalHumanList();

  factory PublicDigitalHumanList.fromJson(Map<String, dynamic> json) =>
      _$PublicDigitalHumanListFromJson(json);

  Map<String, dynamic> toJson() => _$PublicDigitalHumanListToJson(this);
}