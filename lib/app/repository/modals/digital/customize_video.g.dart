// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customize_video.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomizeVideo _$CustomizeVideoFromJson(Map<String, dynamic> json) =>
    CustomizeVideo()
      ..createTime = json['createTime'] as String?
      ..duration = (json['duration'] as num?)?.toInt()
      ..failMsg = json['failMsg'] as String?
      ..id = (json['id'] as num?)?.toInt()
      ..previewUrl = json['previewUrl'] as String?
      ..progress = (json['progress'] as num?)?.toInt()
      ..state = (json['state'] as num?)?.toInt()
      ..subtitleDataUrl = json['subtitleDataUrl'] as String?
      ..updateTime = json['updateTime'] as String?
      ..userId = (json['userId'] as num?)?.toInt()
      ..videoAlias = json['videoAlias'] as String?
      ..videoData = json['videoData'] as String?
      ..videoId = json['videoId'] as String?
      ..videoState = (json['videoState'] as num?)?.toInt()
      ..videoUrl = json['videoUrl'] as String?;

Map<String, dynamic> _$CustomizeVideoToJson(CustomizeVideo instance) =>
    <String, dynamic>{
      'createTime': instance.createTime,
      'duration': instance.duration,
      'failMsg': instance.failMsg,
      'id': instance.id,
      'previewUrl': instance.previewUrl,
      'progress': instance.progress,
      'state': instance.state,
      'subtitleDataUrl': instance.subtitleDataUrl,
      'updateTime': instance.updateTime,
      'userId': instance.userId,
      'videoAlias': instance.videoAlias,
      'videoData': instance.videoData,
      'videoId': instance.videoId,
      'videoState': instance.videoState,
      'videoUrl': instance.videoUrl,
    };

CustomizeVideoList _$CustomizeVideoListFromJson(Map<String, dynamic> json) =>
    CustomizeVideoList()
      ..hasNextPage = json['hasNextPage'] as bool?
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => CustomizeVideo.fromJson(e as Map<String, dynamic>))
          .toList()
      ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$CustomizeVideoListToJson(CustomizeVideoList instance) =>
    <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
