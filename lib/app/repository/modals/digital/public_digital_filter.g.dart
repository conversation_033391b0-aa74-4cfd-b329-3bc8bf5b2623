// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'public_digital_filter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PublicDigitalFilter _$PublicDigitalFilterFromJson(Map<String, dynamic> json) =>
    PublicDigitalFilter()
      ..genderList = (json['genderList'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList()
      ..personTypeMap = json['personTypeMap'] as Map<String, dynamic>?
      ..audioNameList = (json['audioNameList'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList();

Map<String, dynamic> _$PublicDigitalFilterToJson(
        PublicDigitalFilter instance) =>
    <String, dynamic>{
      'genderList': instance.genderList,
      'personTypeMap': instance.personTypeMap,
      'audioNameList': instance.audioNameList,
    };
