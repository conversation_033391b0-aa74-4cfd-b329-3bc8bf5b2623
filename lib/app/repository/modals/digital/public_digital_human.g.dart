// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'public_digital_human.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PublicDigitalHuman _$PublicDigitalHumanFromJson(Map<String, dynamic> json) =>
    PublicDigitalHuman()
      ..audioId = json['audioId'] as String?
      ..audioName = json['audioName'] as String?
      ..audioPreview = json['audioPreview'] as String?
      ..cover = json['cover'] as String?
      ..createTime = json['createTime'] as String?
      ..gender = json['gender'] as String?
      ..height = (json['height'] as num?)?.toInt()
      ..id = (json['id'] as num?)?.toInt()
      ..personId = json['personId'] as String?
      ..personName = json['personName'] as String?
      ..personType = json['personType'] as String?
      ..previewVideoUrl = json['previewVideoUrl'] as String?
      ..state = (json['state'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?
      ..width = (json['width'] as num?)?.toInt();

Map<String, dynamic> _$PublicDigitalHumanToJson(PublicDigitalHuman instance) =>
    <String, dynamic>{
      'audioId': instance.audioId,
      'audioName': instance.audioName,
      'audioPreview': instance.audioPreview,
      'cover': instance.cover,
      'createTime': instance.createTime,
      'gender': instance.gender,
      'height': instance.height,
      'id': instance.id,
      'personId': instance.personId,
      'personName': instance.personName,
      'personType': instance.personType,
      'previewVideoUrl': instance.previewVideoUrl,
      'state': instance.state,
      'updateTime': instance.updateTime,
      'width': instance.width,
    };

PublicDigitalHumanList _$PublicDigitalHumanListFromJson(
        Map<String, dynamic> json) =>
    PublicDigitalHumanList()
      ..hasNextPage = json['hasNextPage'] as bool?
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => PublicDigitalHuman.fromJson(e as Map<String, dynamic>))
          .toList()
      ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$PublicDigitalHumanListToJson(
        PublicDigitalHumanList instance) =>
    <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
