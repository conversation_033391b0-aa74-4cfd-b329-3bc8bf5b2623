import 'package:json_annotation/json_annotation.dart';

part 'upload_img_adivce.g.dart';

@JsonSerializable()
class UploadImgAdivce {
  final String? title;
  final String? imageUrl;

  UploadImgAdivce({
    this.title,
    this.imageUrl,
  });
  
  factory UploadImgAdivce.fromJson(Map<String, dynamic> json) =>
      _$UploadImgAdivceFromJson(json);

  Map<String, dynamic> toJson() => _$UploadImgAdivceToJson(this);
}

@JsonSerializable()
class UploadImgAdivceData {
  final UploadImgAdivce correctExamples;
  final List<UploadImgAdivce> errorExamples;

  UploadImgAdivceData({
    required this.correctExamples,
    required this.errorExamples,
  });
}
