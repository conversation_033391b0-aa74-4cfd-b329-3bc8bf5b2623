import 'package:json_annotation/json_annotation.dart';

part 'audio_item.g.dart';

@JsonSerializable()
class AudioItem {
  /// 音频唯一标识符
  final String id;

  /// 音频标题/名称
  final String title;

  /// 音频URL（服务器返回的URL）
  final String audioUrl;

  /// 音频封面图片URL
  final String? cover;

  /// 音频时长
  final Duration? duration;

  /// 创建时间
  final DateTime? createdAt;

  /// 文件大小（字节）
  final int? fileSize;

  /// 音频格式（如：m4a, mp3, wav等）
  final String? audioFormat;

  AudioItem({
    required this.id,
    required this.title,
    required this.audioUrl,
    this.cover,
    this.duration,
    this.createdAt,
    this.fileSize,
    this.audioFormat,
  });

  factory AudioItem.fromJson(Map<String, dynamic> json) =>
      _$AudioItemFromJson(json);

  Map<String, dynamic> toJson() => _$AudioItemToJson(this);
}
