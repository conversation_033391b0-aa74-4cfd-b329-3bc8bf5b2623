// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_img_adivce.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadImgAdivce _$UploadImgAdivceFromJson(Map<String, dynamic> json) =>
    UploadImgAdivce(
      title: json['title'] as String?,
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$UploadImgAdivceToJson(UploadImgAdivce instance) =>
    <String, dynamic>{
      'title': instance.title,
      'imageUrl': instance.imageUrl,
    };

UploadImgAdivceData _$UploadImgAdivceDataFromJson(Map<String, dynamic> json) =>
    UploadImgAdivceData(
      correctExamples: UploadImgAdivce.fromJson(
          json['correctExamples'] as Map<String, dynamic>),
      errorExamples: (json['errorExamples'] as List<dynamic>)
          .map((e) => UploadImgAdivce.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UploadImgAdivceDataToJson(
        UploadImgAdivceData instance) =>
    <String, dynamic>{
      'correctExamples': instance.correctExamples,
      'errorExamples': instance.errorExamples,
    };
