// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_img_to_video.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AudioImgToVideo _$AudioImgToVideoFromJson(Map<String, dynamic> json) =>
    AudioImgToVideo(
      audioUrl: json['audioUrl'] as String? ?? '',
      caseName: json['caseName'] as String? ?? '',
      cover: json['cover'] as String? ?? '',
      videoUrl: json['videoUrl'] as String? ?? '',
    );

Map<String, dynamic> _$AudioImgToVideoToJson(AudioImgToVideo instance) =>
    <String, dynamic>{
      'audioUrl': instance.audioUrl,
      'caseName': instance.caseName,
      'cover': instance.cover,
      'videoUrl': instance.videoUrl,
    };
