
import 'package:json_annotation/json_annotation.dart';

part 'audio_img_to_video.g.dart';

@JsonSerializable()
class AudioImgToVideo {
  String audioUrl;
  String caseName;
  String cover;
  String videoUrl;
  AudioImgToVideo({
    this.audioUrl = '',
    this.caseName = '',
    this.cover = '',
    this.videoUrl = '',
  });
  factory AudioImgToVideo.fromJson(Map<String, dynamic> json) =>
      _$AudioImgToVideoFromJson(json);
  Map<String, dynamic> toJson() => _$AudioImgToVideoToJson(this);
}