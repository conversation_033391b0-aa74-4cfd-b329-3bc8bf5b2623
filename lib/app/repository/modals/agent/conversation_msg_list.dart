import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/agent/conversation_msg_item.dart';

part 'conversation_msg_list.g.dart';

@JsonSerializable()
class ConversationMsgList {
  String? firstID;
  bool? hasMore;
  List<ConversationMsgItem>? items;
  String? lastID;
  String? logID;
  int? total;

  ConversationMsgList();

  factory ConversationMsgList.fromJson(Map<String, dynamic> json) =>
      _$ConversationMsgListFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationMsgListToJson(this);
}