// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_msg_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConversationMsgList _$ConversationMsgListFromJson(Map<String, dynamic> json) =>
    ConversationMsgList()
      ..firstID = json['firstID'] as String?
      ..hasMore = json['hasMore'] as bool?
      ..items = (json['items'] as List<dynamic>?)
          ?.map((e) => ConversationMsgItem.fromJson(e as Map<String, dynamic>))
          .toList()
      ..lastID = json['lastID'] as String?
      ..logID = json['logID'] as String?
      ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$ConversationMsgListToJson(
        ConversationMsgList instance) =>
    <String, dynamic>{
      'firstID': instance.firstID,
      'hasMore': instance.hasMore,
      'items': instance.items,
      'lastID': instance.lastID,
      'logID': instance.logID,
      'total': instance.total,
    };
