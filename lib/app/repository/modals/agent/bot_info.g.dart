// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bot_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BotInfo _$BotInfoFromJson(Map<String, dynamic> json) => BotInfo()
  ..bot = json['bot'] == null
      ? null
      : Bot.fromJson(json['bot'] as Map<String, dynamic>)
  ..logID = json['logID'] as String?;

Map<String, dynamic> _$BotInfoToJson(BotInfo instance) => <String, dynamic>{
      'bot': instance.bot,
      'logID': instance.logID,
    };

Bot _$<PERSON>(Map<String, dynamic> json) => Bot()
  ..bot_id = json['bot_id'] as String?
  ..description = json['description'] as String?
  ..icon_url = json['icon_url'] as String?
  ..name = json['name'] as String?
  ..onboarding_info = json['onboarding_info'] == null
      ? null
      : OnboardingInfo.fromJson(
          json['onboarding_info'] as Map<String, dynamic>);

Map<String, dynamic> _$BotToJson(Bot instance) => <String, dynamic>{
      'bot_id': instance.bot_id,
      'description': instance.description,
      'icon_url': instance.icon_url,
      'name': instance.name,
      'onboarding_info': instance.onboarding_info,
    };

OnboardingInfo _$OnboardingInfoFromJson(Map<String, dynamic> json) =>
    OnboardingInfo()
      ..prologue = json['prologue'] as String?
      ..suggested_questions = (json['suggested_questions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList();

Map<String, dynamic> _$OnboardingInfoToJson(OnboardingInfo instance) =>
    <String, dynamic>{
      'prologue': instance.prologue,
      'suggested_questions': instance.suggested_questions,
    };
