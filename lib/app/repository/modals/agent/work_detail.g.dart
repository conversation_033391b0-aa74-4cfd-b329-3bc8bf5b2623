// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_detail.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkDetail _$WorkDetailFromJson(Map<String, dynamic> json) => WorkDetail()
  ..createTime = json['createTime'] as String?
  ..id = (json['id'] as num?)?.toInt()
  ..updateTime = json['updateTime'] as String?
  ..workConent = json['workConent'] as String?
  ..workRecordId = (json['workRecordId'] as num?)?.toInt()
  ..workType = (json['workType'] as num?)?.toInt();

Map<String, dynamic> _$WorkDetailToJson(WorkDetail instance) =>
    <String, dynamic>{
      'createTime': instance.createTime,
      'id': instance.id,
      'updateTime': instance.updateTime,
      'workConent': instance.workConent,
      'workRecordId': instance.workRecordId,
      'workType': instance.workType,
    };

WorkDetailList _$WorkDetailListFromJson(Map<String, dynamic> json) =>
    WorkDetailList()
      ..botId = json['botId'] as String?
      ..chatId = json['chatId'] as String?
      ..conversationId = json['conversationId'] as String?
      ..createTime = json['createTime'] as String?
      ..detailList = (json['detailList'] as List<dynamic>?)
          ?.map((e) => WorkDetail.fromJson(e as Map<String, dynamic>))
          .toList()
      ..id = (json['id'] as num?)?.toInt()
      ..includeDetail = (json['includeDetail'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?
      ..userId = (json['userId'] as num?)?.toInt()
      ..workTitle = json['workTitle'] as String?;

Map<String, dynamic> _$WorkDetailListToJson(WorkDetailList instance) =>
    <String, dynamic>{
      'botId': instance.botId,
      'chatId': instance.chatId,
      'conversationId': instance.conversationId,
      'createTime': instance.createTime,
      'detailList': instance.detailList,
      'id': instance.id,
      'includeDetail': instance.includeDetail,
      'updateTime': instance.updateTime,
      'userId': instance.userId,
      'workTitle': instance.workTitle,
    };

WorkList _$WorkListFromJson(Map<String, dynamic> json) => WorkList()
  ..hasNextPage = json['hasNextPage'] as bool?
  ..list = (json['list'] as List<dynamic>?)
      ?.map((e) => WorkDetailList.fromJson(e as Map<String, dynamic>))
      .toList()
  ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$WorkListToJson(WorkList instance) => <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
