import 'package:json_annotation/json_annotation.dart';

part 'conversation_msg_item.g.dart';

@JsonSerializable()
class ConversationMsgItem {
  String? audio;
  String? bot_id;
  String? chat_id;
  String? content;
  String? content_type;
  String? conversation_id;
  int? created_at;
  String? id;
  dynamic meta_data;
  String? reasoning_content;
  String? role;
  String? section_id;
  int? updated_at;

  ConversationMsgItem();

  factory ConversationMsgItem.fromJson(Map<String, dynamic> json) =>
      _$ConversationMsgItemFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationMsgItemToJson(this);
}