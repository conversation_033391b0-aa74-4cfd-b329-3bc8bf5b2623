// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_msg_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConversationMsgItem _$ConversationMsgItemFromJson(Map<String, dynamic> json) =>
    ConversationMsgItem()
      ..audio = json['audio'] as String?
      ..bot_id = json['bot_id'] as String?
      ..chat_id = json['chat_id'] as String?
      ..content = json['content'] as String?
      ..content_type = json['content_type'] as String?
      ..conversation_id = json['conversation_id'] as String?
      ..created_at = (json['created_at'] as num?)?.toInt()
      ..id = json['id'] as String?
      ..meta_data = json['meta_data']
      ..reasoning_content = json['reasoning_content'] as String?
      ..role = json['role'] as String?
      ..section_id = json['section_id'] as String?
      ..updated_at = (json['updated_at'] as num?)?.toInt();

Map<String, dynamic> _$ConversationMsgItemToJson(
        ConversationMsgItem instance) =>
    <String, dynamic>{
      'audio': instance.audio,
      'bot_id': instance.bot_id,
      'chat_id': instance.chat_id,
      'content': instance.content,
      'content_type': instance.content_type,
      'conversation_id': instance.conversation_id,
      'created_at': instance.created_at,
      'id': instance.id,
      'meta_data': instance.meta_data,
      'reasoning_content': instance.reasoning_content,
      'role': instance.role,
      'section_id': instance.section_id,
      'updated_at': instance.updated_at,
    };
