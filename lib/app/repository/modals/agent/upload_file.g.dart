// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_file.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadFile _$UploadFileFromJson(Map<String, dynamic> json) => UploadFile()
  ..fileInfo = json['fileInfo'] == null
      ? null
      : UploadFileInfo.fromJson(json['fileInfo'] as Map<String, dynamic>)
  ..logID = json['logID'] as String?;

Map<String, dynamic> _$UploadFileToJson(UploadFile instance) =>
    <String, dynamic>{
      'fileInfo': instance.fileInfo,
      'logID': instance.logID,
    };

UploadFileInfo _$UploadFileInfoFromJson(Map<String, dynamic> json) =>
    UploadFileInfo()
      ..bytes = (json['bytes'] as num?)?.toInt()
      ..created_at = (json['created_at'] as num?)?.toInt()
      ..file_name = json['file_name'] as String?
      ..id = json['id'] as String?;

Map<String, dynamic> _$UploadFileInfoToJson(UploadFileInfo instance) =>
    <String, dynamic>{
      'bytes': instance.bytes,
      'created_at': instance.created_at,
      'file_name': instance.file_name,
      'id': instance.id,
    };
