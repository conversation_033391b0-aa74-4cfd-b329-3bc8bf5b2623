import 'package:json_annotation/json_annotation.dart';

part 'bot_info.g.dart';

@JsonSerializable()
class BotInfo {
  Bot? bot;
  String? logID;

  BotInfo();

  factory BotInfo.fromJson(Map<String, dynamic> json) =>
      _$BotInfoFromJson(json);

  Map<String, dynamic> toJson() => _$BotInfoToJson(this);
}

@JsonSerializable()
class Bot {
  String? bot_id;
  String? description;
  String? icon_url;
  String? name;
  OnboardingInfo? onboarding_info;

  Bot();

  factory Bot.fromJson(Map<String, dynamic> json) =>
      _$BotFromJson(json);

  Map<String, dynamic> toJson() => _$BotToJson(this);
}

@JsonSerializable()
class OnboardingInfo {
  String? prologue;
  List<String>? suggested_questions;

  OnboardingInfo();

  factory OnboardingInfo.fromJson(Map<String, dynamic> json) =>
      _$OnboardingInfoFromJson(json);

  Map<String, dynamic> toJson() => _$OnboardingInfoToJson(this);
}