// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consume_compute_power.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConsumeComputePower _$ConsumeComputePowerFromJson(Map<String, dynamic> json) =>
    ConsumeComputePower()
      ..botResponse = json['botResponse'] as String?
      ..chatId = json['chatId'] as String?
      ..conversationId = json['conversationId'] as String?
      ..createTime = json['createTime'] as String?
      ..id = (json['id'] as num?)?.toInt()
      ..powerNum = (json['powerNum'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?
      ..userInput = json['userInput'] as String?;

Map<String, dynamic> _$ConsumeComputePowerToJson(
        ConsumeComputePower instance) =>
    <String, dynamic>{
      'botResponse': instance.botResponse,
      'chatId': instance.chatId,
      'conversationId': instance.conversationId,
      'createTime': instance.createTime,
      'id': instance.id,
      'powerNum': instance.powerNum,
      'updateTime': instance.updateTime,
      'userInput': instance.userInput,
    };
