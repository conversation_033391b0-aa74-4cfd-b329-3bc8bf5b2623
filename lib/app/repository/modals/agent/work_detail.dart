import 'package:json_annotation/json_annotation.dart';

part 'work_detail.g.dart';

@JsonSerializable()
class WorkDetail {
  String? createTime;
  int? id;
  String? updateTime;
  String? workConent;
  int? workRecordId;
  int? workType;

  WorkDetail();

  factory WorkDetail.fromJson(Map<String, dynamic> json) =>
      _$WorkDetailFromJson(json);

  Map<String, dynamic> toJson() => _$WorkDetailToJson(this);
}

@JsonSerializable()
class WorkDetailList {
  String? botId;
  String? chatId;
  String? conversationId;
  String? createTime;
  List<WorkDetail>? detailList;
  int? id;
  int? includeDetail;
  String? updateTime;
  int? userId;
  String? workTitle;

  WorkDetailList();

  factory WorkDetailList.fromJson(Map<String, dynamic> json) =>
      _$WorkDetailListFromJson(json);

  Map<String, dynamic> toJson() => _$WorkDetailListToJson(this);
}

@JsonSerializable()
class WorkList {
  bool? hasNextPage;
  List<WorkDetailList>? list;
  int? total;

  WorkList();

  factory WorkList.fromJson(Map<String, dynamic> json) =>
      _$WorkListFromJson(json);

  Map<String, dynamic> toJson() => _$WorkListToJson(this);
}