import 'package:json_annotation/json_annotation.dart';

part 'consume_compute_power.g.dart';

@JsonSerializable()
class ConsumeComputePower {
  String? botResponse;
  String? chatId;
  String? conversationId;
  String? createTime;
  int? id;
  int? powerNum;
  String? updateTime;
  String? userInput;

  ConsumeComputePower();

  factory ConsumeComputePower.fromJson(Map<String, dynamic> json) =>
      _$ConsumeComputePowerFromJson(json);

  Map<String, dynamic> toJson() => _$ConsumeComputePowerToJson(this);
}