import 'package:json_annotation/json_annotation.dart';

part 'order.g.dart';

@JsonSerializable()
class Order {
  int? id;
  String? memo;
  int? orderType;
  String? outTradeNo;
  num? payAmount;
  String? payQrCodeUrl;
  int? payType;
  int? state;
  int? subType;
  String? tradeState;
  String? transactionId;
  int? userId;

  Order();

  factory Order.fromJson(Map<String, dynamic> json) =>
      _$OrderFromJson(json);

  Map<String, dynamic> toJson() => _$OrderToJson(this);
}