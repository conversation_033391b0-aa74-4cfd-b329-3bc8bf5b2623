// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Order _$OrderFromJson(Map<String, dynamic> json) => Order()
  ..id = (json['id'] as num?)?.toInt()
  ..memo = json['memo'] as String?
  ..orderType = (json['orderType'] as num?)?.toInt()
  ..outTradeNo = json['outTradeNo'] as String?
  ..payAmount = json['payAmount'] as num?
  ..payQrCodeUrl = json['payQrCodeUrl'] as String?
  ..payType = (json['payType'] as num?)?.toInt()
  ..state = (json['state'] as num?)?.toInt()
  ..subType = (json['subType'] as num?)?.toInt()
  ..tradeState = json['tradeState'] as String?
  ..transactionId = json['transactionId'] as String?
  ..userId = (json['userId'] as num?)?.toInt();

Map<String, dynamic> _$OrderToJson(Order instance) => <String, dynamic>{
      'id': instance.id,
      'memo': instance.memo,
      'orderType': instance.orderType,
      'outTradeNo': instance.outTradeNo,
      'payAmount': instance.payAmount,
      'payQrCodeUrl': instance.payQrCodeUrl,
      'payType': instance.payType,
      'state': instance.state,
      'subType': instance.subType,
      'tradeState': instance.tradeState,
      'transactionId': instance.transactionId,
      'userId': instance.userId,
    };
