import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/order/package.dart';

part 'package_list.g.dart';

@JsonSerializable()
class PackageList {
  List<Package>? data;

  PackageList();

  factory PackageList.fromJson(Map<String, dynamic> json) =>
      _$PackageListFromJson(json);

  Map<String, dynamic> toJson() => _$PackageListToJson(this);
}