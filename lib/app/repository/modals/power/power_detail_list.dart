import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/power/power_detail.dart';

part 'power_detail_list.g.dart';

@JsonSerializable()
class PowerDetailList {
  bool? hasNextPage;
  List<PowerDetail>? list;
  int? total;

  PowerDetailList();

  factory PowerDetailList.fromJson(Map<String, dynamic> json) =>
      _$PowerDetailListFromJson(json);

  Map<String, dynamic> toJson() => _$PowerDetailListToJson(this);
}
