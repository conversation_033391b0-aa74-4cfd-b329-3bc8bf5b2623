import 'package:json_annotation/json_annotation.dart';

part 'power_detail.g.dart';

@JsonSerializable()
class PowerDetail {
  int? accountId;
  int? accountType;
  String? createTime;
  String? description;
  String? expireTime;
  int? id;
  int? powerBalance;
  int? powerNum;
  int? state;
  String? updateTime;

  PowerDetail();

  factory PowerDetail.fromJson(Map<String, dynamic> json) =>
      _$PowerDetailFromJson(json);

  Map<String, dynamic> toJson() => _$PowerDetailToJson(this);
}