// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'power_item_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PowerItemType _$PowerItemTypeFromJson(Map<String, dynamic> json) =>
    PowerItemType()
      ..createTime = json['createTime'] as String?
      ..id = (json['id'] as num?)?.toInt()
      ..itemName = json['itemName'] as String?
      ..itemType = (json['itemType'] as num?)?.toInt()
      ..powerNum = (json['powerNum'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?;

Map<String, dynamic> _$PowerItemTypeToJson(PowerItemType instance) =>
    <String, dynamic>{
      'createTime': instance.createTime,
      'id': instance.id,
      'itemName': instance.itemName,
      'itemType': instance.itemType,
      'powerNum': instance.powerNum,
      'updateTime': instance.updateTime,
    };
