import 'package:json_annotation/json_annotation.dart';

part 'power_item_type.g.dart';

@JsonSerializable()
class PowerItemType {
  String? createTime;
  int? id;
  String? itemName;
  int? itemType;
  int? powerNum;
  String? updateTime;

  PowerItemType();

  factory PowerItemType.fromJson(Map<String, dynamic> json) =>
      _$PowerItemTypeFromJson(json);

  Map<String, dynamic> toJson() => _$PowerItemTypeToJson(this);
}