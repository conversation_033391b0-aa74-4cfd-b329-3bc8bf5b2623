// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'power_detail_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PowerDetailList _$PowerDetailListFromJson(Map<String, dynamic> json) =>
    PowerDetailList()
      ..hasNextPage = json['hasNextPage'] as bool?
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => PowerDetail.fromJson(e as Map<String, dynamic>))
          .toList()
      ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$PowerDetailListToJson(PowerDetailList instance) =>
    <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
