import 'package:json_annotation/json_annotation.dart';

part 'power_account.g.dart';

@JsonSerializable()
class PowerAccount {
  String? createTime;
  int? id;
  int? powerBalance;
  int? totalPower;
  String? updateTime;
  int? userId;

  PowerAccount();

  factory PowerAccount.fromJson(Map<String, dynamic> json) =>
      _$PowerAccountFromJson(json);

  Map<String, dynamic> toJson() => _$PowerAccountToJson(this);
}