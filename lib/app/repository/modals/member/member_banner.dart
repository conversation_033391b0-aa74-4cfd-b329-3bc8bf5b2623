import 'package:json_annotation/json_annotation.dart';

part 'member_banner.g.dart';

@JsonSerializable()
class MemberBanner {
  int? id;
  int? functionType;
  String? bannerName;
  String? bannerImgUrl;
  String? bannerIconUrl;

  MemberBanner();

  factory MemberBanner.fromJson(Map<String, dynamic> json) =>
      _$MemberBannerFromJson(json);

  Map<String, dynamic> toJson() => _$MemberBannerToJson(this);
}