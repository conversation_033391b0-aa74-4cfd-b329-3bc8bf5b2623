import 'package:json_annotation/json_annotation.dart';

part 'member_benefits.g.dart';

@JsonSerializable()
class MemberBenefits {
  int? id;
  String? iconUrl;
  String? tagUrl;
  String? rightName;
  String? rightDesc;

  MemberBenefits();

  factory MemberBenefits.fromJson(Map<String, dynamic> json) =>
      _$MemberBenefitsFromJson(json);

  Map<String, dynamic> toJson() => _$MemberBenefitsToJson(this);
}