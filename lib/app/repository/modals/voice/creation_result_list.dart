import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/voice/creation_result.dart';

part 'creation_result_list.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.repository.modals.voice
/// @ClassName: creation_result_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/10 14:25
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/10 14:25
/// @UpdateRemark: 更新说明
@JsonSerializable()
class CreationResultList {
  bool? hasNextPage;
  List<CreationResult>? list;
  int? total;

  CreationResultList();

  factory CreationResultList.fromJson(Map<String, dynamic> json) =>
      _$CreationResultListFromJson(json);

  Map<String, dynamic> toJson() => _$CreationResultListToJson(this);
}
