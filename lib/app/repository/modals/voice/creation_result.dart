import 'package:json_annotation/json_annotation.dart';

part 'creation_result.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.repository.modals.voice
/// @ClassName: creation
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/10 11:41
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/10 11:41
/// @UpdateRemark: 更新说明
@JsonSerializable()
class CreationResult {
  int? aiRewriteSwitch;
  String? category;
  String? content;
  String? createTime;
  String? eraStyle;
  String? fileName;
  int? generationState;
  int? id;
  int? model;
  String? novelId;
  String? originFileUrl;
  String? paintingStyle;
  int? processState;
  String? promptUrl;
  String? rewriteFileUrl;
  int? segmentationMaxLength;
  int? state;
  String? updateTime;
  String? videoUrl;
  String? voiceCode;

  CreationResult();

  factory CreationResult.fromJson(Map<String, dynamic> json) =>
      _$CreationResultFromJson(json);

  Map<String, dynamic> toJson() => _$CreationResultToJson(this);
}
