// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'creation_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreationResult _$CreationResultFromJson(Map<String, dynamic> json) =>
    CreationResult()
      ..aiRewriteSwitch = (json['aiRewriteSwitch'] as num?)?.toInt()
      ..category = json['category'] as String?
      ..content = json['content'] as String?
      ..createTime = json['createTime'] as String?
      ..eraStyle = json['eraStyle'] as String?
      ..fileName = json['fileName'] as String?
      ..generationState = (json['generationState'] as num?)?.toInt()
      ..id = (json['id'] as num?)?.toInt()
      ..model = (json['model'] as num?)?.toInt()
      ..novelId = json['novelId'] as String?
      ..originFileUrl = json['originFileUrl'] as String?
      ..paintingStyle = json['paintingStyle'] as String?
      ..processState = (json['processState'] as num?)?.toInt()
      ..promptUrl = json['promptUrl'] as String?
      ..rewriteFileUrl = json['rewriteFileUrl'] as String?
      ..segmentationMaxLength = (json['segmentationMaxLength'] as num?)?.toInt()
      ..state = (json['state'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?
      ..videoUrl = json['videoUrl'] as String?
      ..voiceCode = json['voiceCode'] as String?;

Map<String, dynamic> _$CreationResultToJson(CreationResult instance) =>
    <String, dynamic>{
      'aiRewriteSwitch': instance.aiRewriteSwitch,
      'category': instance.category,
      'content': instance.content,
      'createTime': instance.createTime,
      'eraStyle': instance.eraStyle,
      'fileName': instance.fileName,
      'generationState': instance.generationState,
      'id': instance.id,
      'model': instance.model,
      'novelId': instance.novelId,
      'originFileUrl': instance.originFileUrl,
      'paintingStyle': instance.paintingStyle,
      'processState': instance.processState,
      'promptUrl': instance.promptUrl,
      'rewriteFileUrl': instance.rewriteFileUrl,
      'segmentationMaxLength': instance.segmentationMaxLength,
      'state': instance.state,
      'updateTime': instance.updateTime,
      'videoUrl': instance.videoUrl,
      'voiceCode': instance.voiceCode,
    };
