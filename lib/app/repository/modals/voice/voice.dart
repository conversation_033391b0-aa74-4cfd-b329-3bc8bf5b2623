import 'package:json_annotation/json_annotation.dart';

part 'voice.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.repository.modals.voice
/// @ClassName: voice
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/9 17:04
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/9 17:04
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Voice {
  String? code;
  String? desc;
  String? gender;
  String? lang;

  Voice();

  factory Voice.fromJson(Map<String, dynamic> json) => _$VoiceFromJson(json);

  Map<String, dynamic> toJson() => _$VoiceToJson(this);
}
