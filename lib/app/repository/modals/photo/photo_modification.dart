import 'package:json_annotation/json_annotation.dart';

part 'photo_modification.g.dart';

@JsonSerializable()
class PhotoModification {
  String? aiImgUrl;
  String? caseTitle;
  String? originalImgUrl;
  String? prompt;

  PhotoModification();

  factory PhotoModification.fromJson(Map<String, dynamic> json) =>
      _$PhotoModificationFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoModificationToJson(this);
}