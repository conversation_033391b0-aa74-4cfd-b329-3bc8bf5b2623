import 'package:json_annotation/json_annotation.dart';

part 'photo_portrait.g.dart';

@JsonSerializable()
class PhotoPortraitCategory {
  String? caseName;
  List<PhotoPortraitCategoryDetail>? details;
  int? id;
  int? sort;
  int? state;

  PhotoPortraitCategory();

  factory PhotoPortraitCategory.fromJson(Map<String, dynamic> json) =>
      _$PhotoPortraitCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoPortraitCategoryToJson(this);
}

@JsonSerializable()
class PhotoPortraitCategoryDetail {
  int? caseHeight;
  int? caseId;
  String? caseImage;
  String? casePrompt;
  String? caseTitle;
  int? caseWidth;
  int? id;
  int? sort;
  int? state;

  PhotoPortraitCategoryDetail();

  factory PhotoPortraitCategoryDetail.fromJson(Map<String, dynamic> json) =>
      _$PhotoPortraitCategoryDetailFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoPortraitCategoryDetailToJson(this);
}

@JsonSerializable()
class PhotoPortraitBanner {
  int? id;
  int? caseId;
  String? bannerUrl;
  int? sort;
  int? state;

  PhotoPortraitBanner({
    this.id,
    this.caseId,
    this.bannerUrl,
    this.sort,
    this.state,
  });

  factory PhotoPortraitBanner.fromJson(Map<String, dynamic> json) =>
      _$PhotoPortraitBannerFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoPortraitBannerToJson(this);
}

// @JsonSerializable()
// class PhotoPortraitCategoryPageResponse {
//   int? total;
//   List<PhotoPortraitCategory>? list;
//   int? pageNum;
//   int? pageSize;
//   int? size;

//   PhotoPortraitCategoryPageResponse();

//   factory PhotoPortraitCategoryPageResponse.fromJson(
//           Map<String, dynamic> json) =>
//       _$PhotoPortraitCategoryPageResponseFromJson(json);

//   Map<String, dynamic> toJson() =>
//       _$PhotoPortraitCategoryPageResponseToJson(this);

//   /// 是否有下一页
//   bool get hasNextPage {
//     if (total == null || pageNum == null || pageSize == null) return false;
//     return (pageNum! * pageSize!) < total!;
//   }
// }
