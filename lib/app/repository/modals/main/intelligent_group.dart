import 'package:json_annotation/json_annotation.dart';

part 'intelligent_group.g.dart';

@JsonSerializable()
class IntelligentGroup {
  String? groupName;
  int? id;
  int? sort;

  IntelligentGroup();

  factory IntelligentGroup.fromJson(Map<String, dynamic> json) =>
      _$IntelligentGroupFromJson(json);

  Map<String, dynamic> toJson() => _$IntelligentGroupToJson(this);
}

@JsonSerializable()
class IntelligentGroupList {
  List<IntelligentGroup>? data;

  IntelligentGroupList();

  factory IntelligentGroupList.fromJson(Map<String, dynamic> json) =>
      _$IntelligentGroupListFromJson(json);

  Map<String, dynamic> toJson() => _$IntelligentGroupListToJson(this);
}