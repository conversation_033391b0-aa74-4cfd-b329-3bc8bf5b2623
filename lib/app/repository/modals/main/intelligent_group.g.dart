// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'intelligent_group.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IntelligentGroup _$IntelligentGroupFromJson(Map<String, dynamic> json) =>
    IntelligentGroup()
      ..groupName = json['groupName'] as String?
      ..id = (json['id'] as num?)?.toInt()
      ..sort = (json['sort'] as num?)?.toInt();

Map<String, dynamic> _$IntelligentGroupToJson(IntelligentGroup instance) =>
    <String, dynamic>{
      'groupName': instance.groupName,
      'id': instance.id,
      'sort': instance.sort,
    };

IntelligentGroupList _$IntelligentGroupListFromJson(
        Map<String, dynamic> json) =>
    IntelligentGroupList()
      ..data = (json['data'] as List<dynamic>?)
          ?.map((e) => IntelligentGroup.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$IntelligentGroupListToJson(
        IntelligentGroupList instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
