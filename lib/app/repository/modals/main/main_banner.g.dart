// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_banner.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MainDataBanner _$MainData<PERSON>anner<PERSON>rom<PERSON>son(Map<String, dynamic> json) =>
    MainDataBanner()
      ..id = (json['id'] as num?)?.toInt()
      ..imgUrl = json['imgUrl'] as String?
      ..jumpParam = json['jumpParam'] as String?
      ..jumpType = (json['jumpType'] as num?)?.toInt()
      ..sort = (json['sort'] as num?)?.toInt();

Map<String, dynamic> _$MainData<PERSON>annerToJson(MainDataBanner instance) =>
    <String, dynamic>{
      'id': instance.id,
      'imgUrl': instance.imgUrl,
      'jumpParam': instance.jumpParam,
      'jumpType': instance.jumpType,
      'sort': instance.sort,
    };

MainDataBannerList _$MainDataBannerListFromJson(Map<String, dynamic> json) =>
    MainDataBannerList()
      ..data = (json['data'] as List<dynamic>?)
          ?.map((e) => MainDataBanner.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$MainDataBannerListToJson(MainDataBannerList instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
