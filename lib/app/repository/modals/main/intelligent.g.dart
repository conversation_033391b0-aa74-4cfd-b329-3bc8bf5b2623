// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'intelligent.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Intelligent _$IntelligentFromJson(Map<String, dynamic> json) => Intelligent()
  ..appParam = json['appParam'] as String?
  ..appText = json['appText'] as String?
  ..appTitle = json['appTitle'] as String?
  ..appType = (json['appType'] as num?)?.toInt()
  ..id = (json['id'] as num?)?.toInt()
  ..markImgUrl = json['markImgUrl'] as String?
  ..sort = (json['sort'] as num?)?.toInt()
  ..useNum = (json['useNum'] as num?)?.toInt();

Map<String, dynamic> _$IntelligentToJson(Intelligent instance) =>
    <String, dynamic>{
      'appParam': instance.appParam,
      'appText': instance.appText,
      'appTitle': instance.appTitle,
      'appType': instance.appType,
      'id': instance.id,
      'markImgUrl': instance.markImgUrl,
      'sort': instance.sort,
      'useNum': instance.useNum,
    };

IntelligentList _$IntelligentListFromJson(Map<String, dynamic> json) =>
    IntelligentList()
      ..data = (json['data'] as List<dynamic>?)
          ?.map((e) => Intelligent.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$IntelligentListToJson(IntelligentList instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
