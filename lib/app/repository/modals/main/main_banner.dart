import 'package:json_annotation/json_annotation.dart';

part 'main_banner.g.dart';

@JsonSerializable()
class MainDataBanner {
  int? id;
  String? imgUrl;
  String? jumpParam;
  int? jumpType;
  int? sort;

  MainDataBanner();

  factory MainDataBanner.fromJson(Map<String, dynamic> json) =>
      _$MainDataBannerFromJson(json);

  Map<String, dynamic> toJson() => _$MainDataBannerToJson(this);
}

@JsonSerializable()
class MainDataBannerList {
  List<MainDataBanner>? data;

  MainDataBannerList();

  factory MainDataBannerList.fromJson(Map<String, dynamic> json) =>
      _$MainDataBannerListFromJson(json);

  Map<String, dynamic> toJson() => _$MainDataBannerListToJson(this);
}