import 'package:json_annotation/json_annotation.dart';

part 'intelligent.g.dart';

@JsonSerializable()
class Intelligent {
  String? appParam;
  String? appText;
  String? appTitle;
  int? appType;
  int? id;
  String? markImgUrl;
  int? sort;
  int? useNum;

  Intelligent();

  factory Intelligent.fromJson(Map<String, dynamic> json) =>
      _$IntelligentFromJson(json);

  Map<String, dynamic> toJson() => _$IntelligentToJson(this);
}

@JsonSerializable()
class IntelligentList {
  List<Intelligent>? data;

  IntelligentList();

  factory IntelligentList.fromJson(Map<String, dynamic> json) =>
      _$IntelligentListFromJson(json);

  Map<String, dynamic> toJson() => _$IntelligentListToJson(this);
}