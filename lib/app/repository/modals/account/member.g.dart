// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'member.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Member _$MemberFrom<PERSON>son(Map<String, dynamic> json) => Member()
  ..description = json['description'] as String?
  ..expireTime = json['expireTime'] as String?
  ..hasExpire = json['hasExpire'] as bool?
  ..id = (json['id'] as num?)?.toInt()
  ..memberType = (json['memberType'] as num?)?.toInt()
  ..userId = (json['userId'] as num?)?.toInt();

Map<String, dynamic> _$MemberToJson(Member instance) => <String, dynamic>{
      'description': instance.description,
      'expireTime': instance.expireTime,
      'hasExpire': instance.hasExpire,
      'id': instance.id,
      'memberType': instance.memberType,
      'userId': instance.userId,
    };
