import 'package:json_annotation/json_annotation.dart';

part 'account.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: account_response
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/6 11:20
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/6 11:20
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Account {
  String? token;
  String? tokenPrefix;
  String? username;

  Account();

  factory Account.fromJson(Map<String, dynamic> json) =>
      _$AccountFromJson(json);

  Map<String, dynamic> toJson() => _$AccountToJson(this);
}
