import 'package:json_annotation/json_annotation.dart';

part 'video_case.g.dart';

@JsonSerializable()
class VideoCase {
  String? caseImg1;
  String? caseImg2;
  String? caseName;
  String? casePrompt;
  int? caseType;
  String? caseVideoUrl;
  String? cover;
  int? duration;
  int? fps;
  int? id;
  String? ratio;
  String? resolution;

  VideoCase();

  factory VideoCase.fromJson(Map<String, dynamic> json) =>
      _$VideoCaseFromJson(json);

  Map<String, dynamic> toJson() => _$VideoCaseToJson(this);
}