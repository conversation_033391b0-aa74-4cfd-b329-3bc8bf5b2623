import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/digital/customize_video.dart';
import 'package:text_generation_video/app/repository/modals/digital/public_digital_filter.dart';
import 'package:text_generation_video/app/repository/modals/digital/public_digital_human.dart';

import '../api.dart';

class DigitalService {
  /// 获取公共数字人筛选条件
  static Future<ApiResponse<PublicDigitalFilter>> publicDigitalFilter() async {
    try {
      var response = await HttpUtils.get(Api.publicDigitalFilter);

      BaseResponse<PublicDigitalFilter> result = BaseResponse.fromJson(
        response,
        (json) => PublicDigitalFilter.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取公共数字人列表
  static Future<ApiResponse<PublicDigitalHumanList>> publicDigitalList(
    int pageNum,
    int pageSize, {
    String? gender,
    String? personType,
    String? audioName,
  }) async {
    try {
      Map<String, dynamic> data = {
        "pageNum": pageNum,
        "pageSize": pageSize,
      };

      if (gender != null && gender.isNotEmpty) {
        data["gender"] = gender;
      }

      if (personType != null && personType.isNotEmpty) {
        data["personType"] = personType;
      }

      if (audioName != null && audioName.isNotEmpty) {
        data["audioName"] = audioName;
      }

      var response = await HttpUtils.get(Api.publicDigitalList, params: data);

      BaseResponse<PublicDigitalHumanList> result = BaseResponse.fromJson(
        response,
        (json) => PublicDigitalHumanList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 用户创建自定义视频
  static Future<ApiResponse<bool>> customizeVideo(
    String videoName,
    String videoContent,
    PublicDigitalHuman person,
  ) async {
    try {
      var data = {
        "audio": {
          "language": "cn",
          "tts": {
            "audioMan": person.audioId,
            "speed": 1,
            "text": [videoContent]
          },
          "type": "tts",
          "volume": 100
        },
        "bgColor": "",
        "person": {
          "figureType": person.personType,
          "height": person.height,
          "id": person.personId,
          "width": person.width,
          "x": 0,
          "y": 0
        },
        "screenHeight": person.height,
        "screenWidth": person.width,
        "subtitleConfig": {
          "color": "#FFFFFF",
          "fontId": "d6deed050b0e43e58ac87cf4eccfa295",
          "fontSize": 48,
          "height": 200,
          "show": false,
          "width": person.width,
          "x": 0,
          "y": (person.height ?? 100) - 100
        },
        "videoAlias": videoName,
      };

      var response = await HttpUtils.post(Api.customizeVideo, data: data);

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 重新生成，会返回之前的视频数据
  static Future<ApiResponse<bool>> customizeVideoByVideoData(
    String videoData,
  ) async {
    try {
      var data = videoData;

      var response = await HttpUtils.post(Api.customizeVideo, data: data);

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取自定义视频列表
  static Future<ApiResponse<CustomizeVideoList>> customizeVideoRecord(
    int pageNo,
    int pageSize,
  ) async {
    try {
      Map<String, dynamic> data = {
        "pageNo": pageNo,
        "pageSize": pageSize,
      };

      var response =
          await HttpUtils.get(Api.customizeVideoRecord, params: data);

      BaseResponse<CustomizeVideoList> result = BaseResponse.fromJson(
        response,
        (json) => CustomizeVideoList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取自定义视频消耗算力
  static Future<ApiResponse<int>> customizeVideoConsumePower() async {
    try {
      var response = await HttpUtils.get(Api.customizeVideoConsumePower);

      BaseResponse<int> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 修改自定义视频别名
  static Future<ApiResponse<bool>> customizeVideoUpdateAlias(
    String alias,
    int id,
  ) async {
    try {
      var data = FormData.fromMap({
        "alias": alias,
        "id": id,
      });

      var response = await HttpUtils.post(
        Api.customizeVideoUpdateAlias,
        data: data,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 删除自定义视频
  static Future<ApiResponse<bool>> customizeVideoDelete(
    List<int> ids,
  ) async {
    try {
      var response = await HttpUtils.post(
        Api.customizeVideoDelete,
        data: jsonEncode(ids),
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
