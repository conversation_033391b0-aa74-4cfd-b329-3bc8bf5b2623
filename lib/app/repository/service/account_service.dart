import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/account/member.dart';

import '../api.dart';
import '../modals/account/account.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: account_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/30 17:59
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/30 17:59
/// @UpdateRemark: 更新说明
class AccountService {
  /// 获取验证码
  static Future<ApiResponse<String>> getPhoneVerifyCode(String phone) async {
    try {
      var data = {
        "phone": phone,
      };

      var response = await HttpUtils.get(Api.getPhoneVerifyCode, params: data);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 手机号验证码登录
  static Future<ApiResponse<Account?>> login(
    String phone,
    String code,
  ) async {
    try {
      var data = FormData.fromMap({
        "phone": phone,
        "code": code,
      });

      var response = await HttpUtils.post(Api.phoneLogin, data: data);

      BaseResponse<Account> result = BaseResponse.fromJson(
        response,
        (json) => Account.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取用户会员信息
  static Future<ApiResponse<Member?>> getMemberInfo() async {
    try {
      var response = await HttpUtils.get(Api.getMemberInfo);

      BaseResponse<Member> result = BaseResponse.fromJson(
        response,
        (json) => Member.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
