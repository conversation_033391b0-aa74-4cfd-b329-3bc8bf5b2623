import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/power/power_account.dart';
import 'package:text_generation_video/app/repository/modals/power/power_detail_list.dart';
import 'package:text_generation_video/app/repository/modals/power/power_item_type.dart';

import '../api.dart';

class PowerService {
  /// 获取算力明细列表
  static Future<ApiResponse<PowerDetailList>> getPowerDetailList(
    int pageNum,
    int pageSize,
  ) async {
    try {
      var data = {
        "pageNum": pageNum,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(Api.getPowerDetailList, params: data);

      BaseResponse<PowerDetailList> result = BaseResponse.fromJson(
        response,
        (json) => PowerDetailList.fromJson(json),
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取账户算力余额
  static Future<ApiResponse<PowerAccount>> getAccountPower() async {
    try {
      var response = await HttpUtils.get(Api.getAccountPower);

      BaseResponse<PowerAccount> result = BaseResponse.fromJson(
        response,
        (json) => PowerAccount.fromJson(json),
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取制作消耗算力值
  static Future<ApiResponse<PowerItemType>> getPowerItemType(
      int itemType) async {
    try {
      var data = {
        "itemType": itemType,
      };

      var response = await HttpUtils.get(Api.getByItemType, params: data);

      BaseResponse<PowerItemType> result = BaseResponse.fromJson(
        response,
        (json) => PowerItemType.fromJson(json),
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
