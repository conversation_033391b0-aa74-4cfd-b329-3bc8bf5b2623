import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/main/main_banner.dart';

import '../api.dart';
import '../modals/main/intelligent.dart';
import '../modals/main/intelligent_group.dart';

class MainFeatService {
  /// 获取首页banner
  static Future<ApiResponse<List<MainDataBanner>>> getMainBannerList() async {
    try {
      var response = await HttpUtils.get(Api.listMainBanner);

      BaseResponse<MainDataBannerList> result = BaseResponse.fromJson(
        response,
        (json) => MainDataBannerList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取首页智能体分组
  static Future<ApiResponse<List<IntelligentGroup>>>
      getIntelligentAppGroupList() async {
    try {
      var response = await HttpUtils.get(Api.listIntelligentAppGroup);

      BaseResponse<IntelligentGroupList> result = BaseResponse.fromJson(
        response,
        (json) => IntelligentGroupList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 根据分组ID获取首页智能体列表
  static Future<ApiResponse<List<Intelligent>>> intelligentAppByGroupId(
    int groupId,
  ) async {
    try {
      var data = {
        "groupId": groupId,
      };
      var response = await HttpUtils.get(
        Api.intelligentAppByGroupId,
        params: data,
      );

      BaseResponse<IntelligentList> result = BaseResponse.fromJson(
        response,
        (json) => IntelligentList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
