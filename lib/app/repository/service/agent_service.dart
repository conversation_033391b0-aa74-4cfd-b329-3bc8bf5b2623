import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/agent/bot_info.dart';
import 'package:text_generation_video/app/repository/modals/agent/consume_compute_power.dart';
import 'package:text_generation_video/app/repository/modals/agent/conversation_msg_list.dart';
import 'package:text_generation_video/app/repository/modals/agent/upload_file.dart';
import 'package:text_generation_video/app/repository/modals/agent/work_detail.dart';

import '../api.dart';
import '../modals/agent/conversation.dart';

class AgentService {
  /// 获取智能体会话id
  static Future<ApiResponse<Conversation>> getCozeConversation(
    String botId,
  ) async {
    try {
      var data = {
        "botId": botId,
      };

      var response = await HttpUtils.get(Api.getCozeConversation, params: data);

      BaseResponse<Conversation> result = BaseResponse.fromJson(
        response,
        (json) => Conversation.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取智能体消息列表
  static Future<ApiResponse<ConversationMsgList>> queryConversationMessageList(
    String conversationId,
    int limit, {
    String? beforeId,
    String? afterId,
  }) async {
    try {
      var data = {
        "conversationId": conversationId,
        "limit": limit,
      };
      if (beforeId != null) {
        data["beforeId"] = beforeId;
      }

      if (afterId != null) {
        data["afterId"] = afterId;
      }

      var response = await HttpUtils.get(
        Api.queryConversationMessageList,
        params: data,
      );

      BaseResponse<ConversationMsgList> result = BaseResponse.fromJson(
        response,
        (json) => ConversationMsgList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 上传图片
  static Future<ApiResponse<UploadFile>> uploadFile(
    String filePath,
    String? fileName,
  ) async {
    try {
      var data = FormData.fromMap({
        "file": await MultipartFile.fromFile(
          filePath,
          filename: fileName,
        ),
      });

      var response = await HttpUtils.post(Api.uploadFile, data: data);

      BaseResponse<UploadFile> result = BaseResponse.fromJson(
        response,
        (json) => UploadFile.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取智能体配置信息
  static Future<ApiResponse<BotInfo>> getBotInfo(
    String botId,
  ) async {
    try {
      var data = {
        "botId": botId,
      };

      var response = await HttpUtils.get(Api.getBotInfo, params: data);

      BaseResponse<BotInfo> result = BaseResponse.fromJson(
        response,
        (json) => BotInfo.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 分页查询作品明细
  static Future<ApiResponse<WorkList>> getWorkDetailList(
    String conversationId,
    int pageNum,
    int pageSize,
  ) async {
    try {
      var data = {
        "conversationId": conversationId,
        "pageNum": pageNum,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(Api.getWorkDetailList, params: data);

      BaseResponse<WorkList> result = BaseResponse.fromJson(
        response,
        (json) => WorkList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 根据ChatID获取消耗算力
  static Future<ApiResponse<ConsumeComputePower>> getRecordPowerById(
    String chatId,
  ) async {
    try {
      var data = {
        "chatId": chatId,
      };

      var response = await HttpUtils.get(Api.getRecordPowerById, params: data);

      BaseResponse<ConsumeComputePower> result = BaseResponse.fromJson(
        response,
        (json) => ConsumeComputePower.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
