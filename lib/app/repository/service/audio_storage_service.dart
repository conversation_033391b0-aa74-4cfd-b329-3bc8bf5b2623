import 'package:flutter/foundation.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_item.dart';
import 'package:text_generation_video/utils/prefs_util.dart';

/// 音频存储服务类
/// 负责处理音频列表的本地持久化存储
class AudioStorageService {
  AudioStorageService._internal();

  static final AudioStorageService _instance = AudioStorageService._internal();

  factory AudioStorageService() => _instance;

  /// 保存音频列表到本地存储
  Future<bool> saveAudioList(List<AudioItem> audioList) async {
    try {
      // 将音频列表转换为JSON格式
      final List<Map<String, dynamic>> jsonList =
          audioList.map((item) => item.toJson()).toList();

      // 保存到SharedPreferences
      final result = await PrefsUtil().setJSON(PrefsKeys.audioListKey, jsonList);

      if (result == true) {
        debugPrint('音频列表保存成功，共${audioList.length}条记录');
        return true;
      } else {
        debugPrint('音频列表保存失败');
        return false;
      }
    } catch (e) {
      debugPrint('保存音频列表失败: $e');
      return false;
    }
  }

  /// 从本地存储加载音频列表
   Future<List<AudioItem>> loadAudioList() async {
    try {
      // 从SharedPreferences读取数据
      final dynamic jsonData = PrefsUtil().getJSON(PrefsKeys.audioListKey);

      if (jsonData == null) {
        debugPrint('本地存储中没有音频列表数据');
        return [];
      }

      // 确保数据是List类型
      if (jsonData is! List) {
        debugPrint('音频列表数据格式错误，不是List类型');
        return [];
      }

      // 将JSON数据转换为AudioItem对象列表
      final List<AudioItem> audioList = [];
      for (final item in jsonData) {
        if (item is Map<String, dynamic>) {
          try {
            final audioItem = AudioItem.fromJson(item);
            audioList.add(audioItem);
          } catch (e) {
            debugPrint('解析音频项失败: $e, 数据: $item');
            // 跳过无法解析的项，继续处理其他项
          }
        }
      }

      debugPrint('从本地存储加载音频列表成功，共${audioList.length}条记录');
      return audioList;
    } catch (e) {
      debugPrint('加载音频列表失败: $e');
      return [];
    }
  }

  /// 添加单个音频项到存储
  Future<bool> addAudioItem(AudioItem audioItem) async {
    try {
      final currentList = await loadAudioList();

      // 检查是否已存在相同ID的音频
      final existingIndex =
          currentList.indexWhere((item) => item.id == audioItem.id);

      if (existingIndex != -1) {
        // 如果存在，则更新
        currentList[existingIndex] = audioItem;
        debugPrint('更新音频项: ${audioItem.title}');
      } else {
        // 如果不存在，则添加到列表开头（最新的在前面）
        currentList.insert(0, audioItem);
        debugPrint('添加新音频项: ${audioItem.title}');
      }

      return await saveAudioList(currentList);
    } catch (e) {
      debugPrint('添加音频项失败: $e');
      return false;
    }
  }

  /// 删除音频项
  Future<bool> removeAudioItems(List<String> audioIds) async {
    try {
      final currentList = await loadAudioList();

      // 过滤掉要删除的音频项
      final filteredList =
          currentList.where((item) => !audioIds.contains(item.id)).toList();

      debugPrint('删除音频项: ${audioIds.length}个，剩余: ${filteredList.length}个');

      return await saveAudioList(filteredList);
    } catch (e) {
      debugPrint('删除音频项失败: $e');
      return false;
    }
  }
}
