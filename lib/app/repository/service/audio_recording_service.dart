import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/api.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_item.dart';
import 'package:text_generation_video/app/repository/service/audio_storage_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

/// 处理音频录制上传和处理的服务类
class AudioRecordingService {
  /// 上传录制的音频文件并创建AudioItem
  static Future<AudioItem?> uploadRecordedAudio({
    required String audioFilePath,
    required Duration duration,
    String? customTitle,
    Function(AudioItem)? onAudioAdded,
  }) async {
    final storageService = AudioStorageService();
    final file = File(audioFilePath);

    SmartDialog.showLoading(msg: "上传音频中...");

    try {
      final result = await uploadAudioFile(audioFilePath);

      if (result.status != Status.completed || result.data == null) {
        throw Exception('上传音频失败');
      }
      // 构建初始的 AudioItem
      final uploadedItem = AudioItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: customTitle ?? _generateAudioTitle(duration),
        audioUrl: result.data!,
        duration: duration,
        createdAt: DateTime.now(),
        fileSize: await file.length(),
        audioFormat: audioFilePath.split('.').last.toLowerCase(),
      );

      await storageService.addAudioItem(uploadedItem);
      onAudioAdded?.call(uploadedItem);

      ToastUtil.showToast("音频上传成功");
      return uploadedItem;
    } catch (e) {
      debugPrint('音频上传失败: $e');
      ToastUtil.showToast("音频上传失败");
      return null;
    } finally {
      SmartDialog.dismiss();
      await _cleanupLocalFile(audioFilePath);
    }
  }

  /// 卡通数字人-音频上传
  static Future<ApiResponse<String?>> uploadAudioFile(String filePath) async {
    try {
      var data = FormData.fromMap({
        "file": await MultipartFile.fromFile(filePath),
      });

      var response = await HttpUtils.post(Api.imgUpload, data: data);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 根据时间生成默认标题
  static String _generateAudioTitle(Duration duration) {
    final now = DateTime.now();
    final dateString =
        "${now.year.toString().substring(2)}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}"
        "${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}";
    return dateString;
  }

  /// 清理本地文件
  static Future<void> _cleanupLocalFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        debugPrint("Cleaned up local audio file: $filePath");
      }
    } catch (e) {
      debugPrint("Error cleaning up local file: $e");
    }
  }

  /// 验证音频文件
  static Future<bool> validateAudioFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        ToastUtil.showToast("音频文件不存在");
        return false;
      }

      final fileSize = await file.length();
      const maxSizeBytes = 10 * 1024 * 1024; // 10MB limit

      if (fileSize > maxSizeBytes) {
        ToastUtil.showToast("音频文件过大，请录制较短的音频");
        return false;
      }

      return true;
    } catch (e) {
      debugPrint("音频文件验证失败: $e");
      return false;
    }
  }
}
