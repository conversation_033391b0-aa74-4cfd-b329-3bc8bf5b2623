import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/voice/creation_result.dart';
import 'package:text_generation_video/app/repository/modals/voice/creation_result_list.dart';
import 'package:text_generation_video/app/repository/modals/voice/voice.dart';

import '../api.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.repository.service
/// @ClassName: voice_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/9 17:05
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/9 17:05
/// @UpdateRemark: 更新说明
class VoiceService {
  /// 获取配音任务列表
  static Future<ApiResponse<List<Voice>>> getVoiceList() async {
    try {
      var response = await HttpUtils.get(Api.listVoice);

      BaseResponse<List<Voice>> result = BaseResponse.fromJson(
        response,
        (json) {
          return List.from(json).map((e) => Voice.fromJson(e)).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 开始生成视频
  static Future<ApiResponse<CreationResult>> creationVideo(
    String content,
    String paintingStyle,
    String voiceCode,
  ) async {
    try {
      var data = {
        "aiRewriteSwitch": 1,
        "content": content,
        "model": 1,
        "paintingStyle": paintingStyle,
        "voiceCode": voiceCode,
        "fileName": content.substring(0, 1),
      };

      var response = await HttpUtils.post(Api.creation, data: data);

      BaseResponse<CreationResult> result = BaseResponse.fromJson(
        response,
        (json) => CreationResult.fromJson(json),
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("creationVideo-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取配音任务列表
  static Future<ApiResponse<CreationResultList>> getCreationList(
    int pageNo,
    int pageSize,
  ) async {
    try {
      var data = {
        "pageNo": pageNo,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(Api.creationList, params: data);

      BaseResponse<CreationResultList> result = BaseResponse.fromJson(
        response,
        (json) => CreationResultList.fromJson(json),
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 开始生成视频
  static Future<ApiResponse<bool>> deleteVideos(List<int?> ids) async {
    try {
      var response = await HttpUtils.post(Api.deleteByIds, data: ids);

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("creationVideo-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
