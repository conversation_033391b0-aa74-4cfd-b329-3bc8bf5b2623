import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'dart:ui' as ui;

class DragCardPage extends StatefulWidget {
  final Widget child;

  const DragCardPage({
    super.key,
    required this.child,
  });

  @override
  State<DragCardPage> createState() => _DragCardPageState();
}

class _DragCardPageState extends State<DragCardPage>
    with TickerProviderStateMixin {
  // 位移与缩放状态
  double _dx = 0.0;
  double _dy = 0.0;
  double _scale = 1.0;

  // 阶段控制：是否进入自由拖动（锁定缩放）
  // bool _freeDragMode = false;

  // 动画控制器：回弹 & 离场
  late final AnimationController _rebound;
  late final AnimationController _dismiss;

  Size get _screenSize => MediaQuery.of(context).size;

  // 可调参数（按你口味微调）
  static const double minScale = 0.6; // 阶段1的最小缩放
  // static const double scalePhaseThresholdPx = 80; // 进入自由拖拽的位移阈值
  static const double popThresholdRatio = 0.33; // 关闭阈值：屏宽的 33%
  static const double maxCorner = 22; // 最大圆角
  // static const double bgMinScale = 0.95; // 背景最小缩放（景深）

  @override
  void initState() {
    super.initState();
    _rebound = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 220),
    );
    _dismiss = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 180),
    );
  }

  @override
  void dispose() {
    _rebound.dispose();
    _dismiss.dispose();
    super.dispose();
  }

  void _onPanUpdate(DragUpdateDetails d) {
    setState(() {
      // if (!_freeDragMode) {
      //   // 阶段1：仅水平位移，实时缩放
      //   _dx = (_dx + d.delta.dx).clamp(0.0, double.infinity);
      //   final progress = (_dx / _screenSize.width).clamp(0.0, 1.0);
      //   _scale = 1 - (1 - minScale) * progress;
      //
      //   if (_dx >= scalePhaseThresholdPx) {
      //     _freeDragMode = true; // 达到阈值 → 进入自由拖动
      //     _scale = minScale; // 锁定缩放
      //   }
      // } else {
      //   // 阶段2：自由拖动（上下左右均可）
      //   _dx += d.delta.dx;
      //   _dy += d.delta.dy;
      //   // if (_dx < 0) _dx = 0; // 不允许左侧越界
      // }
      _dx += d.delta.dx;
      _dy += d.delta.dy;
      final progress = (_dx / _screenSize.width).clamp(-1.0, 1.0);
      _scale = (1 - (1 - minScale) * progress.abs()).clamp(0.6, 1);
    });
  }

  void _onPanEnd(DragEndDetails d) async {
    final closeThreshold = _screenSize.width * popThresholdRatio;
    if (_dx.abs() >= closeThreshold) {
      // 关闭：做个离场动画（飞到右侧）
      await _animateDismissThenPop();
    } else {
      // 回弹
      await _animateRebound();
    }
  }

  Future<void> _animateRebound() async {
    final startDx = _dx, startDy = _dy, startScale = _scale;
    _rebound.reset();
    final anim = CurvedAnimation(parent: _rebound, curve: Curves.easeOut);
    _rebound.addListener(() {
      setState(() {
        final t = anim.value;
        _dx = ui.lerpDouble(startDx, 0, t)!;
        _dy = ui.lerpDouble(startDy, 0, t)!;
        _scale = ui.lerpDouble(startScale, 1.0, t)!;
        // if (t == 1.0) _freeDragMode = false;
      });
    });
    await _rebound.forward();
    _rebound.removeListener(() {});
  }

  Future<void> _animateDismissThenPop() async {
    // 动画到屏幕右侧 + 轻微缩小 + 淡出
    // final startDx = _dx, startDy = _dy, startScale = _scale;
    // _dismiss.reset();
    // final anim = CurvedAnimation(parent: _dismiss, curve: Curves.easeIn);
    // _dismiss.addListener(() {
    //   setState(() {
    //     final t = anim.value;
    //     _dx = ui.lerpDouble(startDx, _screenSize.width + 40, t)!;
    //     _dy = ui.lerpDouble(startDy, startDy * 0.9, t)!;
    //     _scale = ui.lerpDouble(startScale, startScale * 0.98, t)!;
    //   });
    // });
    // await _dismiss.forward();
    // _dismiss.removeListener(() {});
    if (mounted) context.pop();
  }

  @override
  Widget build(BuildContext context) {
    // 圆角：按缩放映射
    final double radius = maxCorner * (1 - _scale) / (1 - minScale);

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 半透明遮罩：拖得越远越透明
          IgnorePointer(
            child: Opacity(
              opacity: (0.3 * (1 - (_dx / _screenSize.width).clamp(0, 1))),
              child: Container(color: Colors.black),
            ),
          ),
          // 前景卡片：位移 + 缩放 + 圆角
          Transform.translate(
            offset: Offset(_dx, _dy),
            child: Transform.scale(
              scale: _scale,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(radius),
                child: widget.child,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
