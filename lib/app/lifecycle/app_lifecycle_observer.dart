import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../config/global_config.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: app_lifecycle_observer
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/29 10:53
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 10:53
/// @UpdateRemark: 更新说明

/// 一般情况下识别弹窗在所有路由都会触发
/// 通过判断当前路由 [GlobalConfig.currentRouter] 的方式来阻断识别流程
final List<String> blockRouters = [];

class AppLifecycleObserver with WidgetsBindingObserver {
  AppLifecycleObserver({
    required this.ref,
  });

  final WidgetRef ref;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      debugPrint("应用程序从后台切换到前台");
      // if (!blockRouters.contains(GlobalConfig.currentRouter) &&
      //     GlobalConfig.account != null) {
      //   // ref.read(convertContentProvider.notifier).convert();
      // }
    }
  }
}
