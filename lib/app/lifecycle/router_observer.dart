import 'package:flutter/material.dart';

import '../../config/global_config.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: router_observer
/// @Description: 自定义全局路由监听组件
/// 主要用于识别弹窗功能，提供在某些路由 [app_lifecycle_observer.dart] 中不显示识别弹窗
/// @Author: frankylee
/// @CreateDate: 2023/11/29 16:15
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 16:15
/// @UpdateRemark: 更新说明
class CsRouteObserver extends NavigatorObserver {
  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    var name = route.settings.name?.split("?")[0];
    GlobalConfig.currentRouter = name;
    debugPrint(
      'didPush route: $name,previousRoute:${previousRoute?.settings.name}',
    );
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    var name = previousRoute?.settings.name?.split("?")[0];
    GlobalConfig.currentRouter = name;
    debugPrint(
      'didPop route: $name,previousRoute:${previousRoute?.settings.name}',
    );
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    var name = newRoute?.settings.name?.split("?")[0];
    GlobalConfig.currentRouter = name;
    debugPrint(
      'didReplace newRoute: $name,oldRoute:${oldRoute?.settings.name}',
    );
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);
    var name = previousRoute?.settings.name?.split("?")[0];
    GlobalConfig.currentRouter = name;
    debugPrint(
      'didRemove route: $name,previousRoute:${previousRoute?.settings.name}',
    );
  }

  @override
  void didStartUserGesture(Route route, Route? previousRoute) {
    super.didStartUserGesture(route, previousRoute);
    debugPrint(
      'didStartUserGesture route: ${route.settings.name},previousRoute:${previousRoute?.settings.name}',
    );
  }

  @override
  void didStopUserGesture() {
    super.didStopUserGesture();
    debugPrint('didStopUserGesture');
  }
}
