// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_portrait_provide.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchPhotoPortraitBannerHash() =>
    r'c2167b885ff68e3430f17e23588bacf193514b64';

/// See also [fetchPhotoPortraitBanner].
@ProviderFor(fetchPhotoPortraitBanner)
final fetchPhotoPortraitBannerProvider =
    AutoDisposeFutureProvider<List<PhotoPortraitBanner>?>.internal(
  fetchPhotoPortraitBanner,
  name: r'fetchPhotoPortraitBannerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchPhotoPortraitBannerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchPhotoPortraitBannerRef
    = AutoDisposeFutureProviderRef<List<PhotoPortraitBanner>?>;
String _$fetchphotoPortraitCategoryListHash() =>
    r'f8a9d206ade09f68d6eef5a285ddc2814d586f3c';

/// See also [fetchphotoPortraitCategoryList].
@ProviderFor(fetchphotoPortraitCategoryList)
final fetchphotoPortraitCategoryListProvider =
    AutoDisposeFutureProvider<List<PhotoPortraitCategory>?>.internal(
  fetchphotoPortraitCategoryList,
  name: r'fetchphotoPortraitCategoryListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchphotoPortraitCategoryListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchphotoPortraitCategoryListRef
    = AutoDisposeFutureProviderRef<List<PhotoPortraitCategory>?>;
String _$fetchphotoPortraitDetailHash() =>
    r'1a9e1f17a9daddeb61bc8cb3a695508e3c59278f';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchphotoPortraitDetail].
@ProviderFor(fetchphotoPortraitDetail)
const fetchphotoPortraitDetailProvider = FetchphotoPortraitDetailFamily();

/// See also [fetchphotoPortraitDetail].
class FetchphotoPortraitDetailFamily
    extends Family<AsyncValue<PhotoPortraitCategoryDetail?>> {
  /// See also [fetchphotoPortraitDetail].
  const FetchphotoPortraitDetailFamily();

  /// See also [fetchphotoPortraitDetail].
  FetchphotoPortraitDetailProvider call(
    int caseId,
  ) {
    return FetchphotoPortraitDetailProvider(
      caseId,
    );
  }

  @override
  FetchphotoPortraitDetailProvider getProviderOverride(
    covariant FetchphotoPortraitDetailProvider provider,
  ) {
    return call(
      provider.caseId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchphotoPortraitDetailProvider';
}

/// See also [fetchphotoPortraitDetail].
class FetchphotoPortraitDetailProvider
    extends AutoDisposeFutureProvider<PhotoPortraitCategoryDetail?> {
  /// See also [fetchphotoPortraitDetail].
  FetchphotoPortraitDetailProvider(
    int caseId,
  ) : this._internal(
          (ref) => fetchphotoPortraitDetail(
            ref as FetchphotoPortraitDetailRef,
            caseId,
          ),
          from: fetchphotoPortraitDetailProvider,
          name: r'fetchphotoPortraitDetailProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchphotoPortraitDetailHash,
          dependencies: FetchphotoPortraitDetailFamily._dependencies,
          allTransitiveDependencies:
              FetchphotoPortraitDetailFamily._allTransitiveDependencies,
          caseId: caseId,
        );

  FetchphotoPortraitDetailProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caseId,
  }) : super.internal();

  final int caseId;

  @override
  Override overrideWith(
    FutureOr<PhotoPortraitCategoryDetail?> Function(
            FetchphotoPortraitDetailRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchphotoPortraitDetailProvider._internal(
        (ref) => create(ref as FetchphotoPortraitDetailRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caseId: caseId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<PhotoPortraitCategoryDetail?>
      createElement() {
    return _FetchphotoPortraitDetailProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchphotoPortraitDetailProvider && other.caseId == caseId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caseId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchphotoPortraitDetailRef
    on AutoDisposeFutureProviderRef<PhotoPortraitCategoryDetail?> {
  /// The parameter `caseId` of this provider.
  int get caseId;
}

class _FetchphotoPortraitDetailProviderElement
    extends AutoDisposeFutureProviderElement<PhotoPortraitCategoryDetail?>
    with FetchphotoPortraitDetailRef {
  _FetchphotoPortraitDetailProviderElement(super.provider);

  @override
  int get caseId => (origin as FetchphotoPortraitDetailProvider).caseId;
}

String _$photoPortraitUploadImageHash() =>
    r'6b5712dcbe1874ee5323eef2afd2593f0c7cd55f';

/// See also [PhotoPortraitUploadImage].
@ProviderFor(PhotoPortraitUploadImage)
final photoPortraitUploadImageProvider =
    AutoDisposeNotifierProvider<PhotoPortraitUploadImage, String?>.internal(
  PhotoPortraitUploadImage.new,
  name: r'photoPortraitUploadImageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photoPortraitUploadImageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoPortraitUploadImage = AutoDisposeNotifier<String?>;
String _$photoHistoryHash() => r'491fdecbd2ca651d36c3f9473c3225015fc9e49a';

/// See also [PhotoHistory].
@ProviderFor(PhotoHistory)
final photoHistoryProvider =
    AutoDisposeAsyncNotifierProvider<PhotoHistory, List<String>>.internal(
  PhotoHistory.new,
  name: r'photoHistoryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$photoHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoHistory = AutoDisposeAsyncNotifier<List<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
