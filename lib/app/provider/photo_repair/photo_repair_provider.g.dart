// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_repair_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photoRepairActionHash() => r'df6f0fa935841b047354cf94e6a52af94819b257';

/// See also [PhotoRepairAction].
@ProviderFor(PhotoRepairAction)
final photoRepairActionProvider =
    AutoDisposeNotifierProvider<PhotoRepairAction, void>.internal(
  PhotoRepairAction.new,
  name: r'photoRepairActionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photoRepairActionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoRepairAction = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
