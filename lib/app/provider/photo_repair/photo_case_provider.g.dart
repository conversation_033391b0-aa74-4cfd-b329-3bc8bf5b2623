// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_case_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchModificationCaseHash() =>
    r'885c2498fbb6a782f035542cb65765b0b4a39706';

/// See also [fetchModificationCase].
@ProviderFor(fetchModificationCase)
final fetchModificationCaseProvider =
    AutoDisposeFutureProvider<List<PhotoModification>?>.internal(
  fetchModificationCase,
  name: r'fetchModificationCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchModificationCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchModificationCaseRef
    = AutoDisposeFutureProviderRef<List<PhotoModification>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
