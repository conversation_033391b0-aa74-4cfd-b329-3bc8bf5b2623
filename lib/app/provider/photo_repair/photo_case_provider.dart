import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_modification.dart';

import '../../repository/service/text_to_video_service.dart';

part 'photo_case_provider.g.dart';

// 获取AI改图案例列表
@riverpod
Future<List<PhotoModification>?> fetchModificationCase(Ref ref) async {
  var result = await TextToVideoService.modificationCaseList();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}
