import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/member/member_banner.dart';
import 'package:text_generation_video/app/repository/modals/member/member_benefits.dart';
import 'package:text_generation_video/app/repository/service/account_service.dart';
import 'package:text_generation_video/app/repository/service/member_service.dart';

import '../../repository/modals/account/member.dart';
import '../account/auth_provider.dart';

part 'member_provider.g.dart';

@riverpod
class MemberInfo extends _$MemberInfo {
  @override
  Member? build() {
    getMember();
    return null;
  }

  void getMember() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var memberResult = await AccountService.getMemberInfo();
      if (memberResult.status == Status.completed) {
        state = memberResult.data;
      }
    }
  }
}

// 获取会员页Banner
@riverpod
Future<List<MemberBanner>?> fetchMemberBannerList(Ref ref) async {
  var result = await MemberService.memberBannerList();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

// 获取会员页权益显示列表
@riverpod
Future<List<MemberBenefits>?> fetchMemberBenefitsList(Ref ref) async {
  var result = await MemberService.memberBenefitsList();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}
