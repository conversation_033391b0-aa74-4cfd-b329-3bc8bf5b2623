// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'member_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMemberBannerListHash() =>
    r'9db8c9c8ad3abe6937d1edc7c88d313120124134';

/// See also [fetchMemberBannerList].
@ProviderFor(fetchMemberBannerList)
final fetchMemberBannerListProvider =
    AutoDisposeFutureProvider<List<MemberBanner>?>.internal(
  fetchMemberBannerList,
  name: r'fetchMemberBannerListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMemberBannerListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchMemberBannerListRef
    = AutoDisposeFutureProviderRef<List<MemberBanner>?>;
String _$fetchMemberBenefitsListHash() =>
    r'f0a5a5644370cbe2f1de3874ade277749ebad284';

/// See also [fetchMemberBenefitsList].
@ProviderFor(fetchMemberBenefitsList)
final fetchMemberBenefitsListProvider =
    AutoDisposeFutureProvider<List<MemberBenefits>?>.internal(
  fetchMemberBenefitsList,
  name: r'fetchMemberBenefitsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMemberBenefitsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchMemberBenefitsListRef
    = AutoDisposeFutureProviderRef<List<MemberBenefits>?>;
String _$memberInfoHash() => r'c646a8559265aa56f5341f63fbe7e3050fca2b18';

/// See also [MemberInfo].
@ProviderFor(MemberInfo)
final memberInfoProvider =
    AutoDisposeNotifierProvider<MemberInfo, Member?>.internal(
  MemberInfo.new,
  name: r'memberInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$memberInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MemberInfo = AutoDisposeNotifier<Member?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
