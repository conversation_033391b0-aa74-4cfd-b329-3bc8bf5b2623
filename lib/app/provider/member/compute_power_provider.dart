import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/repository/modals/power/power_account.dart';
import 'package:text_generation_video/app/repository/modals/power/power_detail.dart';
import 'package:text_generation_video/app/repository/service/power_service.dart';
import 'package:ui_widgets/ui_widgets.dart' show LoadState;

part 'compute_power_provider.g.dart';

@riverpod
class UserPowerBalance extends _$UserPowerBalance {
  @override
  PowerAccount? build() {
    loadData();
    return null;
  }

  /// 加载数据
  void loadData() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await PowerService.getAccountPower();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }
}

/// 算力明细列表
/// 页长
const int pageSize = 20;

/// 算力明细列表结果
class PowerListResult {
  final int pageNo;
  final List<PowerDetail?>? powerDetailList;
  final LoadState? loadState;

  const PowerListResult({
    this.pageNo = 1,
    this.powerDetailList,
    this.loadState,
  });

  PowerListResult copyWith({
    int? page,
    List<PowerDetail?>? powerDetailList,
    LoadState? loadState,
  }) {
    return PowerListResult(
      pageNo: page ?? pageNo,
      powerDetailList: powerDetailList ?? this.powerDetailList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class PowerRecord extends _$PowerRecord {
  @override
  PowerListResult build() {
    state = const PowerListResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await PowerService.getPowerDetailList(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      state = state.copyWith(
        powerDetailList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await PowerService.getPowerDetailList(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          powerDetailList: [...?state.powerDetailList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
