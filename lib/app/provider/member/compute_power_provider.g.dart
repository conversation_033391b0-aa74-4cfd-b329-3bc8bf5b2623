// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'compute_power_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userPowerBalanceHash() => r'aa330686bf266c8e98eefb0689737afe00e45a8c';

/// See also [UserPowerBalance].
@ProviderFor(UserPowerBalance)
final userPowerBalanceProvider =
    AutoDisposeNotifierProvider<UserPowerBalance, PowerAccount?>.internal(
  UserPowerBalance.new,
  name: r'userPowerBalanceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userPowerBalanceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserPowerBalance = AutoDisposeNotifier<PowerAccount?>;
String _$powerRecordHash() => r'266fd6df2ba898e388fd9584e310075ad0fea0b1';

/// See also [PowerRecord].
@ProviderFor(PowerRecord)
final powerRecordProvider =
    AutoDisposeNotifierProvider<PowerRecord, PowerListResult>.internal(
  PowerRecord.new,
  name: r'powerRecordProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$powerRecordHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PowerRecord = AutoDisposeNotifier<PowerListResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
