// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'digital_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$publicDigitalFilterListHash() =>
    r'59776b2b99b18b97d6a720fcfd7d3163895e6e2d';

/// See also [PublicDigitalFilterList].
@ProviderFor(PublicDigitalFilterList)
final publicDigitalFilterListProvider = AutoDisposeNotifierProvider<
    PublicDigitalFilterList, List<DigitalTab>>.internal(
  PublicDigitalFilterList.new,
  name: r'publicDigitalFilterListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$publicDigitalFilterListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PublicDigitalFilterList = AutoDisposeNotifier<List<DigitalTab>>;
String _$publicDigitalFilterSelectHash() =>
    r'6a2852fa0c81dda4e690676cc7320a2f92016d4d';

/// See also [PublicDigitalFilterSelect].
@ProviderFor(PublicDigitalFilterSelect)
final publicDigitalFilterSelectProvider = AutoDisposeNotifierProvider<
    PublicDigitalFilterSelect, SelectorFilter>.internal(
  PublicDigitalFilterSelect.new,
  name: r'publicDigitalFilterSelectProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$publicDigitalFilterSelectHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PublicDigitalFilterSelect = AutoDisposeNotifier<SelectorFilter>;
String _$publicDigitalHumanListHash() =>
    r'78a4cd228b7095d23512fc1e1648047acbbf1d7d';

/// See also [PublicDigitalHumanList].
@ProviderFor(PublicDigitalHumanList)
final publicDigitalHumanListProvider = AutoDisposeNotifierProvider<
    PublicDigitalHumanList, PublicDigitalListResult>.internal(
  PublicDigitalHumanList.new,
  name: r'publicDigitalHumanListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$publicDigitalHumanListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PublicDigitalHumanList = AutoDisposeNotifier<PublicDigitalListResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
