import 'package:flutter/cupertino.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/digital/public_digital_human.dart';
import 'package:text_generation_video/app/repository/service/digital_service.dart';
import 'package:ui_widgets/ui_widgets.dart' show LoadState;

import 'digital_video_provider.dart';

part 'digital_provider.g.dart';

// 获取公共数字人筛选条件
class DigitalTab {
  final String id;
  final String tabName;
  final List<DigitalChildTab>? childList;

  DigitalTab(
    this.id,
    this.tabName, {
    this.childList,
  });
}

class DigitalChildTab {
  final String id;
  final String tabName;
  final String? key;

  DigitalChildTab(this.id, this.tabName, {this.key});
}

@riverpod
class PublicDigitalFilterList extends _$PublicDigitalFilterList {
  @override
  List<DigitalTab> build() {
    return [
      DigitalTab("100", "全部"),
      DigitalTab("200", "性别", childList: [DigitalChildTab("200", "性别（全部）")]),
      DigitalTab("personTypeMap_all", "形象类型",
          childList: [DigitalChildTab("personTypeMap_all", "形象类型（全部）")]),
      DigitalTab("400", "形象特色",
          childList: [DigitalChildTab("400", "形象特色（全部）")]),
    ];
  }

  void getFilter() async {
    // 筛选列表
    var result = await DigitalService.publicDigitalFilter();
    if (result.status == Status.completed) {
      var filterData = result.data;

      // 性别
      var genderList = filterData?.genderList;
      var genderChildList = [DigitalChildTab("200", "性别（全部）")];
      if (genderList != null && genderList.isNotEmpty) {
        for (var i = 0; i < genderList.length; i++) {
          genderChildList
              .add(DigitalChildTab("${200 + i + 1}", genderList[i] ?? ""));
        }
      }

      // 形象类型
      var personTypeMap = filterData?.personTypeMap;
      var personChildList = [DigitalChildTab("personTypeMap_all", "形象类型（全部）")];
      if (personTypeMap != null) {
        for (var entry in personTypeMap.entries) {
          final key = entry.key;
          final value = entry.value;
          personChildList.add(DigitalChildTab(key, value, key: key));
        }
      }

      // 形象特色
      var audioNameList = filterData?.audioNameList;
      var audioNameChildList = [DigitalChildTab("400", "形象特色（全部）")];
      if (audioNameList != null && audioNameList.isNotEmpty) {
        for (var i = 0; i < audioNameList.length; i++) {
          audioNameChildList
              .add(DigitalChildTab("${400 + i + 1}", audioNameList[i] ?? ""));
        }
      }
      state = [
        DigitalTab("100", "全部"),
        DigitalTab("200", "性别", childList: genderChildList),
        DigitalTab("personTypeMap_all", "形象类型", childList: personChildList),
        DigitalTab("400", "形象特色", childList: audioNameChildList),
      ];
    }
  }
}

// 选中的条件
class SelectorFilter {
  final DigitalChildTab? gender;
  final DigitalChildTab? person;
  final DigitalChildTab? audio;

  const SelectorFilter({
    this.gender,
    this.person,
    this.audio,
  });

  SelectorFilter copyWith({
    DigitalChildTab? gender,
    DigitalChildTab? person,
    DigitalChildTab? audio,
  }) {
    return SelectorFilter(
      gender: gender ?? this.gender,
      person: person ?? this.person,
      audio: audio ?? this.audio,
    );
  }
}

@riverpod
class PublicDigitalFilterSelect extends _$PublicDigitalFilterSelect {
  @override
  SelectorFilter build() {
    return SelectorFilter(
      gender: DigitalChildTab("200", "性别（全部）"),
      person: DigitalChildTab("personTypeMap_all", "形象类型（全部）"),
      audio: DigitalChildTab("400", "形象特色（全部）"),
    );
  }

  void setSelectAll() {
    state = SelectorFilter(
      gender: DigitalChildTab("200", "性别（全部）"),
      person: DigitalChildTab("personTypeMap_all", "形象类型（全部）"),
      audio: DigitalChildTab("400", "形象特色（全部）"),
    );
  }

  void setSelect(DigitalChildTab tab) {
    debugPrint("setSelect: ${tab.id}");
    if (tab.id.startsWith("2")) {
      state = state.copyWith(gender: tab);
    } else if (tab.id.startsWith("4")) {
      state = state.copyWith(audio: tab);
    } else {
      state = state.copyWith(person: tab);
    }
  }
}

/// 公共数字人列表
/// 页长
const int pageSize = 20;

/// 公共数字人列表结果
class PublicDigitalListResult {
  final int pageNo;
  final List<PublicDigitalHuman?>? publicDigitalList;
  final LoadState? loadState;

  const PublicDigitalListResult({
    this.pageNo = 1,
    this.publicDigitalList,
    this.loadState,
  });

  PublicDigitalListResult copyWith({
    int? page,
    List<PublicDigitalHuman?>? publicDigitalList,
    LoadState? loadState,
  }) {
    return PublicDigitalListResult(
      pageNo: page ?? pageNo,
      publicDigitalList: publicDigitalList ?? this.publicDigitalList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class PublicDigitalHumanList extends _$PublicDigitalHumanList {
  @override
  PublicDigitalListResult build() {
    state = const PublicDigitalListResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    var filterData = ref.watch(publicDigitalFilterSelectProvider);
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await DigitalService.publicDigitalList(
      state.pageNo,
      pageSize,
      gender:
          filterData.gender?.id == "200" ? null : filterData.gender?.tabName,
      personType: filterData.person?.id == "personTypeMap_all"
          ? null
          : filterData.person?.key,
      audioName:
          filterData.audio?.id == "400" ? null : filterData.audio?.tabName,
    );
    if (result.status == Status.completed) {
      state = state.copyWith(
        publicDigitalList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );

      var firstDigital = result.data?.list?.first;
      ref
          .watch(publicSelectorDigitalProvider.notifier)
          .setDigital(firstDigital);
    }
  }

  /// 加载更多
  void loadMore() async {
    var filterData = ref.read(publicDigitalFilterSelectProvider);
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await DigitalService.publicDigitalList(
      state.pageNo,
      pageSize,
      gender:
          filterData.gender?.id == "200" ? null : filterData.gender?.tabName,
      personType: filterData.person?.id == "personTypeMap_all"
          ? null
          : filterData.person?.key,
      audioName:
          filterData.audio?.id == "400" ? null : filterData.audio?.tabName,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          publicDigitalList: [
            ...?state.publicDigitalList,
            ...?result.data?.list
          ],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
