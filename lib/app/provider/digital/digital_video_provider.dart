import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/repository/modals/digital/customize_video.dart';
import 'package:text_generation_video/app/repository/service/digital_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:ui_widgets/ui_widgets.dart' show LoadState;

import '../../repository/modals/digital/public_digital_human.dart';
import '../../repository/service/account_service.dart';
import '../../repository/service/power_service.dart';
import '../../view/creation/dialog/confirm_commit_dialog.dart';
import '../../view/creation/dialog/member_check_dialog.dart';
import '../../view/home/<USER>/operate_video_dialog.dart';

part 'digital_video_provider.g.dart';

// 当前选中的数字人
@riverpod
class PublicSelectorDigital extends _$PublicSelectorDigital {
  @override
  PublicDigitalHuman? build() {
    return null;
  }

  void setDigital(PublicDigitalHuman? digital) {
    state = digital;
  }
}

// 视频名称
@riverpod
class PublicDigitalVideoName extends _$PublicDigitalVideoName {
  @override
  String? build() {
    return null;
  }

  void setVideoName(String? name) {
    state = name;
  }
}

// 视频文案
List<String> randomContentList = [
  "大家好！现在在你们面前的我，其实不是真实的我，这是我的超写实数字人分身。你看他的形象动作甚至口型，都可以做到和我一模一样。他能代替我出镜，帮我做视频。以后我会经常用我的数字分身和大家见面！",
  "00后愿意为兴趣负债，70后坚持货比三家，哪种消费观更科学？现在我发起一个家庭消费实验：输入你的年龄和上月非必需消费占比，AI将模拟20年后的资产变化曲线。参与即有机会获得三代同堂理财课，让每代人都找到消费平衡点。",
  "我们编码情感却不该量化温情/构筑元宇宙更要守护烟火气/当老人颤巍巍戴上VR眼镜/孩童在智能农场播种物理定律/愿每个二进制跳动的心脏/都留有0与1之外的柔软缓冲带",
  "冬天吃的草莓夏天为何更甜？揭秘冷链中的‘呼吸跃变’：采摘时7分熟的水果，在-1℃运输中仍会代谢释放香气物质。通过对比5大电商平台的锁鲜技术，教你挑选既新鲜又环保的跨季水果，附赠家庭保鲜实验室手册。",
  "所有女生注意！某某旗舰店福利天花板来了！这款母婴级除菌空调，老人房可开启无风感模式，游戏党适配急速制冷，下单即赠十年保修金卡！今晚8点蹲直播间，前50名送戴森吹风机，库存只够3分钟，家人们，拼手速的时候到了！",
  "今日快讯：NASA公布2025火星基地模拟实验结果。中国‘祝融号’在乌托邦平原发现含水矿物，老年志愿者在封闭舱内成功种植27种蔬菜。科技板块受此影响上涨2.3%，教育界呼吁将太空生存纳入中小学选修课。点击订阅星际移民周报。",
  "年轻人认为科技连接世界，长辈觉得科技疏远亲情，谁对谁错？数字人发起观点对抗赛：上传30秒短视频陈述立场，AI将生成辩证分析报告，优秀参与者可加入跨代际科技伦理研讨会。",
];

@riverpod
class PublicDigitalVideoContent extends _$PublicDigitalVideoContent {
  @override
  String? build() {
    return null;
  }

  void setVideoContent(String? content) {
    state = content;
  }

  void randomContent(TextEditingController controller) {
    final random = Random();
    final randomItem =
        randomContentList[random.nextInt(randomContentList.length)];
    state = randomItem;
    controller.text = randomItem;
  }
}

// 用户创建视频
@riverpod
class DigitalMakeVideo extends _$DigitalMakeVideo {
  @override
  void build() {
    return;
  }

  // 消耗算力
  Future<int?> customizeVideoConsumePower() async {
    SmartDialog.showLoading(msg: "加载中...");
    var powerItemTypeResult = await DigitalService.customizeVideoConsumePower();
    SmartDialog.dismiss();
    if (powerItemTypeResult.status == Status.completed) {
      return powerItemTypeResult.data;
    }
    ToastUtil.showToast(powerItemTypeResult.exception!.getMessage());
    return null;
  }

  Future<bool> memberCheck(Function membership, int? powerItem) async {
    SmartDialog.showLoading(msg: "加载中...");

    // 判断是否是会员
    var memberResult = await AccountService.getMemberInfo();
    if (memberResult.status == Status.error) {
      SmartDialog.dismiss();
      ToastUtil.showToast(memberResult.exception!.getMessage());
      return false;
    }
    if (memberResult.data == null ||
        (memberResult.data != null && memberResult.data?.hasExpire == true)) {
      SmartDialog.dismiss();
      // 非会员或者会员过期
      bool state = memberResult.data == null;
      MemberCheckDialog.showCheckDialog(membership, state);
      return false;
    }

    // 判断算力是否充足
    var accountPowerResult = await PowerService.getAccountPower();
    if (accountPowerResult.status == Status.error) {
      SmartDialog.dismiss();
      ToastUtil.showToast(accountPowerResult.exception!.getMessage());
      return false;
    }
    // 剩余值
    var powerBalance = accountPowerResult.data?.powerBalance;
    if (accountPowerResult.status == Status.error ||
        powerItem == null ||
        powerBalance == null) {
      SmartDialog.dismiss();
      ToastUtil.showToast("算力值获取失败");
      return false;
    }
    // 算力值不足
    if (powerBalance < powerItem) {
      SmartDialog.dismiss();
      ConfirmCommitDialog.powerLessCommit(membership, powerItem: powerItem);
      return false;
    }

    SmartDialog.dismiss();
    return true;
  }

  // 用户创建视频
  void createVideo(Function success, Function membership) async {
    // 确认弹窗
    var consumePower = await customizeVideoConsumePower();
    if (consumePower == null) return;
    var confirm = await ConfirmCommitDialog.confirmCommit(consumePower);
    if (confirm != true) return;

    // 查询会员状态和剩余算力
    // 非会员和算力不足无法发送
    var checkMember = await memberCheck(membership, consumePower);
    if (!checkMember) return;

    var videoName = ref.read(publicDigitalVideoNameProvider);
    var videoContent = ref.read(publicDigitalVideoContentProvider);
    var digital = ref.read(publicSelectorDigitalProvider);
    if (videoName == null || videoName.isEmpty) return;
    if (videoContent == null || videoContent.isEmpty) return;
    if (digital == null) return;
    SmartDialog.showLoading(msg: "提交中...");
    var result = await DigitalService.customizeVideo(
      videoName,
      videoContent,
      digital,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      if (result.data == true) {
        success();
        ref.read(customizeVideoRecordProvider.notifier).loadData();
      } else {
        ToastUtil.showToast("提交失败");
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  // 用户重新创建视频
  // 视频返回失败时，点击查看原因后可以重新生成
  void reCreateVideo(
    Function success,
    Function membership,
    CustomizeVideo customizeVideo,
  ) async {
    // 确认弹窗
    var consumePower = await customizeVideoConsumePower();
    if (consumePower == null) return;
    var confirm = await ConfirmCommitDialog.confirmCommit(consumePower);
    if (confirm != true) return;

    // 查询会员状态和剩余算力
    // 非会员和算力不足无法发送
    var checkMember = await memberCheck(membership, consumePower);
    if (!checkMember) return;

    var videoData = customizeVideo.videoData;
    if (videoData == null) return;
    SmartDialog.showLoading(msg: "提交中...");
    var result = await DigitalService.customizeVideoByVideoData(
      videoData,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      if (result.data == true) {
        success();
        ref.read(customizeVideoRecordProvider.notifier).loadData();
      } else {
        ToastUtil.showToast("提交失败");
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  // 修改视频别名
  void updateVideoAlias(int? id, String? oldName) async {
    var data = await UpdateVideoAliasDialog.updateVideo(oldName);
    debugPrint("UpdateVideoAliasDialog: $data");
    if (id == null || data == null) return;
    if (oldName == data) return;
    SmartDialog.showLoading(msg: "加载中...");
    var result = await DigitalService.customizeVideoUpdateAlias(data, id);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      if (result.data == true) {
        ToastUtil.showToast("修改成功");
        ref.read(customizeVideoRecordProvider.notifier).loadData();
      } else {
        ToastUtil.showToast("修改失败");
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  // 删除视频
  void deleteVideo(int? id) async {
    var data = await DeleteCustomizeVideoDialog.deleteVideo();
    debugPrint("deleteVideo: $data");
    if (id == null || data != true) return;
    SmartDialog.showLoading(msg: "删除中...");
    var result = await DigitalService.customizeVideoDelete([id]);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      if (result.data == true) {
        ToastUtil.showToast("删除成功");
        ref.read(customizeVideoRecordProvider.notifier).loadData();
      } else {
        ToastUtil.showToast("删除失败");
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

/// 自定义视频记录列表
/// 页长
const int pageSize = 20;

/// 自定义视频记录结果
class CustomizeVideoListResult {
  final int pageNo;
  final List<CustomizeVideo?>? videoList;
  final LoadState? loadState;

  const CustomizeVideoListResult({
    this.pageNo = 1,
    this.videoList,
    this.loadState,
  });

  CustomizeVideoListResult copyWith({
    int? page,
    List<CustomizeVideo?>? videoList,
    LoadState? loadState,
  }) {
    return CustomizeVideoListResult(
      pageNo: page ?? pageNo,
      videoList: videoList ?? this.videoList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class CustomizeVideoRecord extends _$CustomizeVideoRecord {
  @override
  CustomizeVideoListResult build() {
    state = const CustomizeVideoListResult();
    loadData();
    return state;
  }

  /// 加载数据
  Future<void> loadData({bool showToast = false}) async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      state = state.copyWith(
        page: 1,
        loadState: null,
      );
      var result = await DigitalService.customizeVideoRecord(
        state.pageNo,
        pageSize,
      );
      if (result.status == Status.completed) {
        var list = result.data?.list;
        if (list != null && list.isNotEmpty) {
          list = [CustomizeVideo()..id = -100, ...list];
        }
        state = state.copyWith(
          videoList: list,
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
        if (showToast) {
          ToastUtil.showToast("刷新成功");
        }
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await DigitalService.customizeVideoRecord(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          videoList: [...?state.videoList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
