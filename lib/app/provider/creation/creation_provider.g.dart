// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'creation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchVoiceListHash() => r'2ad0860242bd05759805462fed7f4f5e20f3201c';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: text_generation_video
/// @Package: app.provider.creation
/// @ClassName: creation_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/9 17:13
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/9 17:13
/// @UpdateRemark: 更新说明
/// 获取配音列表
///
/// Copied from [fetchVoiceList].
@ProviderFor(fetchVoiceList)
final fetchVoiceListProvider = AutoDisposeFutureProvider<List<Voice>?>.internal(
  fetchVoiceList,
  name: r'fetchVoiceListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchVoiceListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchVoiceListRef = AutoDisposeFutureProviderRef<List<Voice>?>;
String _$currentVoiceHash() => r'37c7a77bcb17606b6037eef54a82a312eff1d030';

/// 配音选择
///
/// Copied from [CurrentVoice].
@ProviderFor(CurrentVoice)
final currentVoiceProvider =
    AutoDisposeNotifierProvider<CurrentVoice, Voice?>.internal(
  CurrentVoice.new,
  name: r'currentVoiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentVoiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentVoice = AutoDisposeNotifier<Voice?>;
String _$currentVideoStyleHash() => r'f78c36a09f227f23e85f78f7ccd6b32d9539d34d';

/// See also [CurrentVideoStyle].
@ProviderFor(CurrentVideoStyle)
final currentVideoStyleProvider =
    AutoDisposeNotifierProvider<CurrentVideoStyle, VideoStyle>.internal(
  CurrentVideoStyle.new,
  name: r'currentVideoStyleProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentVideoStyleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentVideoStyle = AutoDisposeNotifier<VideoStyle>;
String _$creationVideoHash() => r'2f0e4077387ebbe4d84335161a9ab1ed464e97dc';

/// 合成
///
/// Copied from [CreationVideo].
@ProviderFor(CreationVideo)
final creationVideoProvider =
    AutoDisposeNotifierProvider<CreationVideo, void>.internal(
  CreationVideo.new,
  name: r'creationVideoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$creationVideoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreationVideo = AutoDisposeNotifier<void>;
String _$deleteProductHash() => r'f7bc17ab6439e6c0d6a16f859fcd45aab7ec8a91';

/// 删除作品
///
/// Copied from [DeleteProduct].
@ProviderFor(DeleteProduct)
final deleteProductProvider =
    AutoDisposeNotifierProvider<DeleteProduct, bool>.internal(
  DeleteProduct.new,
  name: r'deleteProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$deleteProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteProduct = AutoDisposeNotifier<bool>;
String _$deleteProductListHash() => r'ec007c5b3d54e3dfac530876e6cf13f775bf6cbb';

/// See also [DeleteProductList].
@ProviderFor(DeleteProductList)
final deleteProductListProvider = AutoDisposeNotifierProvider<DeleteProductList,
    List<CreationResult>>.internal(
  DeleteProductList.new,
  name: r'deleteProductListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$deleteProductListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteProductList = AutoDisposeNotifier<List<CreationResult>>;
String _$creationRecordHash() => r'8ed07e3af73cb78120d1085bc0daf7c4f6572ffa';

/// See also [CreationRecord].
@ProviderFor(CreationRecord)
final creationRecordProvider =
    AutoDisposeNotifierProvider<CreationRecord, CreationListResult>.internal(
  CreationRecord.new,
  name: r'creationRecordProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$creationRecordHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreationRecord = AutoDisposeNotifier<CreationListResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
