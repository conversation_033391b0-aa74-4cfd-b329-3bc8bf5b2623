import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gal/gal.dart';
import 'package:ms_http/ms_http.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/provider/member/compute_power_provider.dart';
import 'package:text_generation_video/app/repository/modals/voice/creation_result.dart';
import 'package:text_generation_video/app/repository/modals/voice/voice.dart';
import 'package:text_generation_video/app/repository/service/account_service.dart';
import 'package:text_generation_video/app/repository/service/power_service.dart';
import 'package:text_generation_video/app/repository/service/voice_service.dart';
import 'package:text_generation_video/app/view/creation/dialog/confirm_commit_dialog.dart';
import 'package:text_generation_video/app/view/creation/dialog/member_check_dialog.dart';
import 'package:text_generation_video/app/view/home/<USER>/operate_video_dialog.dart';
import 'package:text_generation_video/utils/platform_util.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:path/path.dart' as p;
import 'package:ui_widgets/ui_widgets.dart' show LoadState;

part 'creation_provider.g.dart';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: text_generation_video
/// @Package: app.provider.creation
/// @ClassName: creation_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/9 17:13
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/9 17:13
/// @UpdateRemark: 更新说明

/// 获取配音列表
@riverpod
Future<List<Voice>?> fetchVoiceList(Ref ref) async {
  var result = await VoiceService.getVoiceList();
  if (result.status == Status.completed) {
    if (result.data != null && result.data!.isNotEmpty) {
      ref.watch(currentVoiceProvider.notifier).selectVoice(result.data!.first);
    }
    return result.data;
  } else {
    ToastUtil.showToast(result.exception!.getMessage());
  }
  return null;
}

/// 配音选择
@riverpod
class CurrentVoice extends _$CurrentVoice {
  @override
  Voice? build() {
    return null;
  }

  void selectVoice(Voice voice) {
    state = voice;
  }
}

/// 视频风格
class VideoStyle {
  String value;
  String name;
  String img;

  VideoStyle(this.name, this.value, this.img);
}

final List<VideoStyle> videoStyleList = [
  VideoStyle(
    "现代动漫",
    "Modern-style Chinese comic",
    "https://alicdn.msmds.cn/style_picture/zhongguodongman.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "现代写实",
    "Chinese modern style",
    "https://alicdn.msmds.cn/style_picture/zhongguoxiandaixieshi.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "古风写实",
    "Tang Dynasty-style ancient Chinese",
    "https://alicdn.msmds.cn/style_picture/zhongguogufengxieshi.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "校园",
    "campus, Literature, youth",
    "https://alicdn.msmds.cn/style_picture/xiaoyuanfengge.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "未来科技",
    "Futuristic technology",
    "https://alicdn.msmds.cn/style_picture/weilaikejigan.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "黑丝二次元",
    "Black Pantyhose 2D",
    "https://alicdn.msmds.cn/style_picture/heisierciyuanfengge.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "新海诚",
    "Makoto Shinkai's style, , Manga",
    "https://alicdn.msmds.cn/style_picture/xinhaicheng.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "迪士尼",
    "Disney",
    "https://alicdn.msmds.cn/style_picture/dishini.png?x-oss-process=image/resize,p_20",
  ),
  VideoStyle(
    "皮克斯",
    "Pixar Art",
    "https://alicdn.msmds.cn/style_picture/pikesi.png?x-oss-process=image/resize,p_20",
  ),
];

@riverpod
class CurrentVideoStyle extends _$CurrentVideoStyle {
  @override
  VideoStyle build() {
    return videoStyleList.first;
  }

  void selectVideoStyle(VideoStyle videoStyle) {
    state = videoStyle;
  }
}

/// 合成
@riverpod
class CreationVideo extends _$CreationVideo {
  @override
  void build() {
    return;
  }

  /// 开始合成
  void creation(
    BuildContext context,
    String content,
    Function onSuccess,
    Function membership,
  ) async {
    var voice = ref.read(currentVoiceProvider);
    var videoStyle = ref.read(currentVideoStyleProvider);
    if (content.isEmpty) {
      ToastUtil.showToast("请输入提示内容");
      return;
    }
    if (voice == null) {
      ToastUtil.showToast("请选择配音");
      return;
    }

    SmartDialog.showLoading(msg: "加载中...");
    // 判断是否是会员
    var memberResult = await AccountService.getMemberInfo();
    if (memberResult.status == Status.error) {
      SmartDialog.dismiss();
      ToastUtil.showToast(memberResult.exception!.getMessage());
      return;
    }
    if (memberResult.data == null ||
        (memberResult.data != null && memberResult.data?.hasExpire == true)) {
      SmartDialog.dismiss();
      // 非会员或者会员过期
      bool state = memberResult.data == null;
      MemberCheckDialog.showCheckDialog(membership, state);
      return;
    }

    // 判断算力是否充足
    var powerItemTypeResult = await PowerService.getPowerItemType(1);
    var accountPowerResult = await PowerService.getAccountPower();
    // 消耗值
    var powerItem = powerItemTypeResult.data?.powerNum;
    // 剩余值
    var powerBalance = accountPowerResult.data?.powerBalance;
    debugPrint("power-item: $powerItem");
    debugPrint("power-balance: $powerBalance");
    if (powerItemTypeResult.status == Status.error ||
        accountPowerResult.status == Status.error ||
        powerItem == null ||
        powerBalance == null) {
      SmartDialog.dismiss();
      ToastUtil.showToast("算力值获取失败");
      return;
    }
    // 算力值不足
    if (powerBalance < powerItem) {
      SmartDialog.dismiss();
      ConfirmCommitDialog.powerLessCommit(membership, powerItem: powerItem);
      return;
    }

    // 确认是否提交
    SmartDialog.dismiss();
    var confirmCommit = await ConfirmCommitDialog.confirmCommit(powerItem);
    debugPrint("confirmCommit: $confirmCommit");
    if (confirmCommit == null || confirmCommit == false) return;

    // 提交制作
    SmartDialog.showLoading(msg: "加载中...");
    var result = await VoiceService.creationVideo(
      content,
      videoStyle.value,
      voice.code ?? "",
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      onSuccess.call();
      ref.read(creationRecordProvider.notifier).loadData();
      ref.read(userPowerBalanceProvider.notifier).loadData();
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  /// 下载图片到手机
  void downloadPhoto(String? url) async {
    if (url == null) return;
    SmartDialog.showLoading(msg: "下载中...");
    try {
      bool hasAccess = await Gal.hasAccess(toAlbum: true);
      if (!hasAccess) {
        hasAccess = await Gal.requestAccess(toAlbum: true);
      }

      // 尝试获取文件扩展名
      final tempDir = await getTemporaryDirectory();
      final uri = Uri.parse(url);
      String? fileName =
          uri.pathSegments.isNotEmpty ? uri.pathSegments.last : 'image';
      fileName = fileName.split('?').first; // 去除 query
      if (!fileName.contains('.')) {
        fileName += '.jpg'; // fallback 扩展名
      }
      final filePath = p.join(tempDir.path, fileName);
      if (hasAccess) {
        await Dio().download(url, filePath);
        await Gal.putImage(filePath);
        ToastUtil.showToast("下载成功，已保存到相册");
      } else {
        ToastUtil.showToast("未授权存储权限，取消下载");
      }
    } catch (e) {
      debugPrint("downloadVideo-e: $e");
      ToastUtil.showToast("下载失败");
    }
    SmartDialog.dismiss();
  }

  /// 下载视频到手机
  void downloadVideo(String url) async {
    SmartDialog.showLoading(msg: "下载中...");
    try {
      final videoPath = '${Directory.systemTemp.path}/demo.mp4';
      bool hasAccess = false;
      if (PlatformUtils.isIOS) {
        hasAccess = await Gal.hasAccess(toAlbum: true);
        if (!hasAccess) {
          hasAccess = await Gal.requestAccess(toAlbum: true);
        }
      } else if (PlatformUtils.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkInt = androidInfo.version.sdkInt;
        if (sdkInt >= 33) {
          var permissionStatus = await Permission.photos.request(); // READ_MEDIA_IMAGES
          hasAccess = permissionStatus.isGranted;
        } else if (sdkInt >= 30) {
          var permissionStatus = await Permission.manageExternalStorage.request(); // MANAGE_EXTERNAL_STORAGE
          hasAccess = permissionStatus.isGranted;
        } else {
          var permissionStatus = await Permission.storage.request(); // READ/WRITE_EXTERNAL_STORAGE
          hasAccess = permissionStatus.isGranted;
        }
      }

      if (hasAccess) {
        await Dio().download(url, videoPath);
        await Gal.putVideo(videoPath);
        ToastUtil.showToast("下载成功，已保存到相册");
      } else {
        ToastUtil.showToast("未授权存储权限，取消下载");
      }
    } catch (e) {
      debugPrint("downloadVideo-e: $e");
      ToastUtil.showToast("下载失败");
    }
    SmartDialog.dismiss();
  }

  /// 下载文件到手机
  void downloadFile(String url, String fileName) async {
    SmartDialog.showLoading(msg: "下载中...");
    try {
      Directory dir = await getApplicationDocumentsDirectory();
      String savePath = '${dir.path}/$fileName';
      await Dio().download(url, savePath);
      ToastUtil.showToast("下载成功，已保存到文件");
    } catch (e) {
      debugPrint("downloadFile-e: $e");
      ToastUtil.showToast("下载失败");
    }
    SmartDialog.dismiss();
  }
}

/// 删除作品
@riverpod
class DeleteProduct extends _$DeleteProduct {
  @override
  bool build() {
    return false;
  }

  /// 设置编辑状态
  void setManagerState(bool value) {
    state = value;
  }
}

@riverpod
class DeleteProductList extends _$DeleteProductList {
  @override
  List<CreationResult> build() {
    return [];
  }

  /// 设置编辑状态
  void updateDeleteList(CreationResult value) {
    var index = state.indexWhere((element) => element.id == value.id);
    if (index != -1) {
      /// 存在，从删除列表中移除
      state.removeAt(index);
      state = [...state];
    } else {
      /// 不存在，添加到删除列表中
      state = [value, ...state];
    }
  }

  /// 删除
  void deleteVideos() async {
    if (state.isEmpty) {
      ToastUtil.showToast("请选择要删除的作品");
      return;
    }
    var confirm = await DeleteVideoDialog.confirmDelete();
    if (confirm == true) {
      SmartDialog.showLoading(msg: "删除中...");
      var ids = state.map((e) => e.id).toList();
      var result = await VoiceService.deleteVideos(ids);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        ToastUtil.showToast("删除成功");
        ref.read(creationRecordProvider.notifier).loadData();
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }
}

/// 作品列表查询
/// 页长
const int pageSize = 20;

/// 作品列表结果
class CreationListResult {
  final int pageNo;
  final List<CreationResult?>? creations;
  final LoadState? loadState;

  const CreationListResult({
    this.pageNo = 1,
    this.creations,
    this.loadState,
  });

  CreationListResult copyWith({
    int? page,
    List<CreationResult?>? creations,
    LoadState? loadState,
  }) {
    return CreationListResult(
      pageNo: page ?? pageNo,
      creations: creations ?? this.creations,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class CreationRecord extends _$CreationRecord {
  @override
  CreationListResult build() {
    state = const CreationListResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData({bool showRefreshToast = false}) async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      state = state.copyWith(
        page: 1,
        loadState: null,
      );
      var result = await VoiceService.getCreationList(
        state.pageNo,
        pageSize,
      );
      if (result.status == Status.completed) {
        if (showRefreshToast) {
          ToastUtil.showToast("刷新成功");
        }
        state = state.copyWith(
          creations: result.data?.list ?? [],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await VoiceService.getCreationList(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          creations: [...?state.creations, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
