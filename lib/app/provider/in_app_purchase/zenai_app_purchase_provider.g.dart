// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'zenai_app_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$zenAiAppPurchaseProductHash() =>
    r'0fb714636f3d7230df6d68beba9fab18b96386e1';

/// See also [ZenAiAppPurchaseProduct].
@ProviderFor(ZenAiAppPurchaseProduct)
final zenAiAppPurchaseProductProvider = AutoDisposeNotifierProvider<
    ZenAiAppPurchaseProduct, ZenAiPurchaseData>.internal(
  ZenAiAppPurchaseProduct.new,
  name: r'zenAiAppPurchaseProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchaseProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchaseProduct = AutoDisposeNotifier<ZenAiPurchaseData>;
String _$zenAiAppPurchaseHash() => r'f00db77227023556246f0e7a8e2e94fbac97da49';

/// See also [ZenAiAppPurchase].
@ProviderFor(ZenAiAppPurchase)
final zenAiAppPurchaseProvider =
    AutoDisposeNotifierProvider<ZenAiAppPurchase, void>.internal(
  ZenAiAppPurchase.new,
  name: r'zenAiAppPurchaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchase = AutoDisposeNotifier<void>;
String _$zenAiConsentAgreementHash() =>
    r'980c521ee16d6fa4d1d5bb80ff9e9b20e7677f64';

/// See also [ZenAiConsentAgreement].
@ProviderFor(ZenAiConsentAgreement)
final zenAiConsentAgreementProvider =
    AutoDisposeNotifierProvider<ZenAiConsentAgreement, bool>.internal(
  ZenAiConsentAgreement.new,
  name: r'zenAiConsentAgreementProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiConsentAgreementHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiConsentAgreement = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
