// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$appPurchaseHash() => r'8a19f90a597a22d5b81e8a18c02163dd423215c3';

/// See also [AppPurchase].
@ProviderFor(AppPurchase)
final appPurchaseProvider = AutoDisposeNotifierProvider<AppPurchase,
    List<CustomAppleProduct>?>.internal(
  AppPurchase.new,
  name: r'appPurchaseProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$appPurchaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppPurchase = AutoDisposeNotifier<List<CustomAppleProduct>?>;
String _$currentAppleProductHash() =>
    r'feb0d9ccfd48367c132b4604708c3fdcf83e60e9';

/// See also [CurrentAppleProduct].
@ProviderFor(CurrentAppleProduct)
final currentAppleProductProvider = AutoDisposeNotifierProvider<
    CurrentAppleProduct, CustomAppleProduct?>.internal(
  CurrentAppleProduct.new,
  name: r'currentAppleProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAppleProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentAppleProduct = AutoDisposeNotifier<CustomAppleProduct?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
