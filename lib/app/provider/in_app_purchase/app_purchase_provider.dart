// 苹果支付产品
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/provider/member/member_provider.dart';
import 'package:text_generation_video/app/repository/modals/order/package.dart';

import '../../../config/config.dart';
import '../../../utils/platform_util.dart';
import '../../../utils/toast_util.dart';
import '../../navigation/router.dart';
import '../../repository/service/member_service.dart';
import '../member/compute_power_provider.dart';

part 'app_purchase_provider.g.dart';

enum ApplePayProduct {
  weeklyMember(1, "WeeklyMember"),
  monthlyMember(2, "MonthlyMember"),
  yearMember(3, "YearMember");

  const ApplePayProduct(this.id, this.productId);

  final int id;
  final String productId;
}

class CustomAppleProduct {
  String productId;
  Package? package;
  ProductDetails? details;

  CustomAppleProduct(this.productId, this.package, this.details);
}

@riverpod
class AppPurchase extends _$AppPurchase {
  @override
  List<CustomAppleProduct>? build() {
    return null;
  }

  void initInAppPurchase() async {
    StreamSubscription<List<PurchaseDetails>>? subscription;

    // 监听
    final Stream<List<PurchaseDetails>> purchaseUpdated =
        InAppPurchase.instance.purchaseStream;
    subscription = purchaseUpdated.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        _listenToPurchaseUpdated(purchaseDetailsList);
      },
      onDone: () {
        subscription?.cancel();
      },
      onError: (error) {
        ToastUtil.showToast("购买失败");
      },
    );

    // resumePurchase();
  }

  // 恢复购买
  void resumePurchase() {
    InAppPurchase.instance.restorePurchases();
  }

  // 购买状态更新
  void _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    for (final PurchaseDetails purchase in  purchaseDetailsList) {
      if (purchase.status == PurchaseStatus.pending) {
        // 等待支付完成
        _handlePending();
      } else if (purchase.status == PurchaseStatus.canceled) {
        // 取消支付
        _handleCancel(purchase);
      } else if (purchase.status == PurchaseStatus.error) {
        // 购买失败
        _handleError(purchase.error);
      } else if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        debugPrint('purchase.status: ${purchase.status}');
        // 完成购买，到服务器验证
        if (PlatformUtils.isIOS) {
          var applePurchase = purchase as AppStorePurchaseDetails;
          checkApplePayInfo(
            applePurchase,
            needCancelDialog: purchase.status == PurchaseStatus.purchased,
          );
        }
      }
    }
  }

  // pending
  void _handlePending() {
    debugPrint("apple pay: 支付中");
  }

  // canceled
  void _handleCancel(PurchaseDetails purchase) {
    debugPrint("apple pay: 取消支付");
    SmartDialog.dismiss();
    InAppPurchase.instance.completePurchase(purchase);
  }

  // error
  void _handleError(IAPError? e) {
    SmartDialog.dismiss();
    debugPrint("apple pay: 支付失败-${e?.code},${e?.message}");
  }

  // apple支付验证
  void checkApplePayInfo(
    AppStorePurchaseDetails appstoreDetail, {
    bool needCancelDialog = true,
  }) async {
    var userData = ref.read(authProvider);
    if (userData == null) return;
    // 创建订单
    SmartDialog.showLoading(msg: "加载中...");
    var subType = ApplePayProduct.values
        .firstWhere((e) => e.productId == appstoreDetail.productID)
        .id;
    var orderRes = await MemberService.buyPackage(
      1,
      3,
      subType,
    );
    if (orderRes.status == Status.completed) {
      // 服务器验证
      var result = await MemberService.applePayVerify(
        Config.appleVerifyMethod,
        orderRes.data?.outTradeNo,
        appstoreDetail.verificationData.serverVerificationData,
      );
      if (result.status == Status.completed) {
        debugPrint("pay status: ${result.data?.state}");
        if (result.data?.state == 1) {
          // 支付验证完成，发放会员资格
          debugPrint("验证完成，请发放会员资格");
          if (needCancelDialog &&
              navigatorKey.currentContext?.canPop() == true) {
            navigatorKey.currentContext?.pop();
          }
          ToastUtil.showToast("会员开通成功", state: true);
          // 刷新用户会员信息
          ref.read(memberInfoProvider.notifier).getMember();
          // 刷新算力值
          ref.read(userPowerBalanceProvider.notifier).loadData();
        } else {
          ToastUtil.showToast("购买失败");
        }
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    } else {
      ToastUtil.showToast(orderRes.exception!.getMessage());
    }
    SmartDialog.dismiss();

    // 完成
    await InAppPurchase.instance.completePurchase(appstoreDetail);
  }

  // 加载所有可购买产品
  Future<List<CustomAppleProduct>?> loadAllProducts() async {
    SmartDialog.showLoading(msg: "加载中...");

    // 服务器配置的包
    var serverPackagesResult = await MemberService.getAllPackage();
    var serverPackagesData = serverPackagesResult.data;
    if (serverPackagesData == null || (serverPackagesData.isEmpty)) {
      ToastUtil.showToast("没有可购买商品");
      SmartDialog.dismiss();
      return null;
    }

    // 内购是否可用
    final bool available = await InAppPurchase.instance.isAvailable();
    if (!available) {
      ToastUtil.showToast("无法连接到商店");
      SmartDialog.dismiss();
      return null;
    }

    // if (PlatformUtils.isIOS) {
    //   final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
    //   _inAppPurchase
    //       .getPlatformAddition();
    //   await iosPlatformAddition.setDelegate(ExamplePaymentQueueDelegate());
    // }

    // 根据服务器上的数据获取产品ID列表
    // 以下获取产品ID需要替换服务器返回的
    List<String> productIds =
        ApplePayProduct.values.map((e) => e.productId).toList();
    debugPrint("loadAllProducts: ${productIds.toSet()}");
    // 请求苹果服务器产品列表
    final ProductDetailsResponse response =
        await InAppPurchase.instance.queryProductDetails(productIds.toSet());

    if (response.error != null) {
      debugPrint("loadAllProducts-err: ${response.error}");
      debugPrint("loadAllProducts-err: ${response.error?.message}");
      ToastUtil.showToast("商品加载失败");
      SmartDialog.dismiss();
      return null;
    }

    debugPrint("notFoundIDs: ${response.notFoundIDs}");
    // if (response.notFoundIDs.isNotEmpty) {
    //   ToastUtil.showToast("没有找到商品");
    //   SmartDialog.dismiss();
    //   return null;
    // }

    // 商品列表
    List<ProductDetails> products = response.productDetails;
    if (products.isEmpty) {
      ToastUtil.showToast("没有可购买商品");
      SmartDialog.dismiss();
      return null;
    }

    List<CustomAppleProduct> newList = [];
    for (var i = 0; i < serverPackagesData.length; i++) {
      var detail = serverPackagesData[i];
      var package = products.firstWhere((e) => e.id == detail.appleProductId);
      newList.add(CustomAppleProduct(package.id, detail, package));
    }

    state = newList;
    ref
        .watch(currentAppleProductProvider.notifier)
        .setCurrentProduct(newList.first);
    SmartDialog.dismiss();
    return newList;
  }

  // 购买指定产品
  void purchaseById() async {
    try {
      var customAppleProduct = ref.read(currentAppleProductProvider);
      if (customAppleProduct?.details != null) {
        // 拉起支付
        SmartDialog.showLoading(msg: "支付中...");
        var payResult = await InAppPurchase.instance.buyNonConsumable(
          purchaseParam:
              PurchaseParam(productDetails: customAppleProduct!.details!),
        );
        debugPrint("purchaseById: $payResult");
        if (payResult) {}
      } else {
        ToastUtil.showToast("请选择购买产品");
      }
    } catch (e) {
      SmartDialog.dismiss();
      debugPrint("apple pay: 购买失败-${e.toString()}");
    }
  }
}

// 当前选择的产品
@riverpod
class CurrentAppleProduct extends _$CurrentAppleProduct {
  @override
  CustomAppleProduct? build() {
    return null;
  }

  void setCurrentProduct(CustomAppleProduct product) {
    debugPrint("setCurrentProduct: ${product.productId}");
    state = product;
  }
}
