// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_case_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchPopularVideoCaseHash() =>
    r'c9788420cdaf6c9fc138cef7991e063bc7540ab5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchPopularVideoCase].
@ProviderFor(fetchPopularVideoCase)
const fetchPopularVideoCaseProvider = FetchPopularVideoCaseFamily();

/// See also [fetchPopularVideoCase].
class FetchPopularVideoCaseFamily extends Family<AsyncValue<List<VideoCase>?>> {
  /// See also [fetchPopularVideoCase].
  const FetchPopularVideoCaseFamily();

  /// See also [fetchPopularVideoCase].
  FetchPopularVideoCaseProvider call(
    int caseType,
  ) {
    return FetchPopularVideoCaseProvider(
      caseType,
    );
  }

  @override
  FetchPopularVideoCaseProvider getProviderOverride(
    covariant FetchPopularVideoCaseProvider provider,
  ) {
    return call(
      provider.caseType,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPopularVideoCaseProvider';
}

/// See also [fetchPopularVideoCase].
class FetchPopularVideoCaseProvider
    extends AutoDisposeFutureProvider<List<VideoCase>?> {
  /// See also [fetchPopularVideoCase].
  FetchPopularVideoCaseProvider(
    int caseType,
  ) : this._internal(
          (ref) => fetchPopularVideoCase(
            ref as FetchPopularVideoCaseRef,
            caseType,
          ),
          from: fetchPopularVideoCaseProvider,
          name: r'fetchPopularVideoCaseProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchPopularVideoCaseHash,
          dependencies: FetchPopularVideoCaseFamily._dependencies,
          allTransitiveDependencies:
              FetchPopularVideoCaseFamily._allTransitiveDependencies,
          caseType: caseType,
        );

  FetchPopularVideoCaseProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caseType,
  }) : super.internal();

  final int caseType;

  @override
  Override overrideWith(
    FutureOr<List<VideoCase>?> Function(FetchPopularVideoCaseRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPopularVideoCaseProvider._internal(
        (ref) => create(ref as FetchPopularVideoCaseRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caseType: caseType,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VideoCase>?> createElement() {
    return _FetchPopularVideoCaseProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPopularVideoCaseProvider && other.caseType == caseType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caseType.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPopularVideoCaseRef
    on AutoDisposeFutureProviderRef<List<VideoCase>?> {
  /// The parameter `caseType` of this provider.
  int get caseType;
}

class _FetchPopularVideoCaseProviderElement
    extends AutoDisposeFutureProviderElement<List<VideoCase>?>
    with FetchPopularVideoCaseRef {
  _FetchPopularVideoCaseProviderElement(super.provider);

  @override
  int get caseType => (origin as FetchPopularVideoCaseProvider).caseType;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
