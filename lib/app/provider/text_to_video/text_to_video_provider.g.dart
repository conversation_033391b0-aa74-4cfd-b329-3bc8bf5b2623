// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_to_video_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$videoEditTextHash() => r'c453afb117d298bc07154b6f86f29e5db4460e50';

/// See also [VideoEditText].
@ProviderFor(VideoEditText)
final videoEditTextProvider =
    AutoDisposeNotifierProvider<VideoEditText, TextEditingController>.internal(
  VideoEditText.new,
  name: r'videoEditTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoEditTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VideoEditText = AutoDisposeNotifier<TextEditingController>;
String _$videoScaleParamHash() => r'4bb25585047440c69778c9a15606ca7e2979f158';

/// See also [VideoScaleParam].
@ProviderFor(VideoScaleParam)
final videoScaleParamProvider =
    AutoDisposeNotifierProvider<VideoScaleParam, VideoScaleParamData>.internal(
  VideoScaleParam.new,
  name: r'videoScaleParamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoScaleParamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VideoScaleParam = AutoDisposeNotifier<VideoScaleParamData>;
String _$videoDurationParamHash() =>
    r'c7247506f403ec32b3255b656f3a29426393c2be';

/// See also [VideoDurationParam].
@ProviderFor(VideoDurationParam)
final videoDurationParamProvider = AutoDisposeNotifierProvider<
    VideoDurationParam, VideoDurationParamData>.internal(
  VideoDurationParam.new,
  name: r'videoDurationParamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoDurationParamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VideoDurationParam = AutoDisposeNotifier<VideoDurationParamData>;
String _$videoClarityParamHash() => r'f233d60f4bd6b28080c3e1b7f38f8ecb2663adc4';

/// See also [VideoClarityParam].
@ProviderFor(VideoClarityParam)
final videoClarityParamProvider = AutoDisposeNotifierProvider<VideoClarityParam,
    VideoClarityParamData>.internal(
  VideoClarityParam.new,
  name: r'videoClarityParamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoClarityParamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VideoClarityParam = AutoDisposeNotifier<VideoClarityParamData>;
String _$textToVideoActionHash() => r'89ecffd1d5942ea887fc409df31ee745a1e2e6b8';

/// See also [TextToVideoAction].
@ProviderFor(TextToVideoAction)
final textToVideoActionProvider =
    AutoDisposeNotifierProvider<TextToVideoAction, void>.internal(
  TextToVideoAction.new,
  name: r'textToVideoActionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$textToVideoActionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TextToVideoAction = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
