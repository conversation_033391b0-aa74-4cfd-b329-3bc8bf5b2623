import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/video/video_case.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';

part 'video_case_provider.g.dart';

// 获取热门案例列表
@riverpod
Future<List<VideoCase>?> fetchPopularVideoCase(Ref ref, int caseType) async {
  var result = await TextToVideoService.listVideoCase(caseType);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}