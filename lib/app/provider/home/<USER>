// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_features_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mainBannerDataHash() => r'44d2cb03fa9d71c10a0ae4564415645341d11195';

/// See also [MainBannerData].
@ProviderFor(MainBannerData)
final mainBannerDataProvider =
    AutoDisposeNotifierProvider<MainBannerData, ManBannerResult?>.internal(
  MainBannerData.new,
  name: r'mainBannerDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mainBannerDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MainBannerData = AutoDisposeNotifier<ManBannerResult?>;
String _$mainIntelligentGroupDataHash() =>
    r'ae0640752ad2ed5f64664f4ddd8f3d7af40eab92';

/// ===================首页分组列表=========================///
///
/// Copied from [MainIntelligentGroupData].
@ProviderFor(MainIntelligentGroupData)
final mainIntelligentGroupDataProvider = AutoDisposeNotifierProvider<
    MainIntelligentGroupData, List<IntelligentGroup>?>.internal(
  MainIntelligentGroupData.new,
  name: r'mainIntelligentGroupDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mainIntelligentGroupDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MainIntelligentGroupData
    = AutoDisposeNotifier<List<IntelligentGroup>?>;
String _$mainIntelligentListDataHash() =>
    r'7ccce7f4935b94dae003b7cbc585fa9d870c5ec8';

/// See also [MainIntelligentListData].
@ProviderFor(MainIntelligentListData)
final mainIntelligentListDataProvider = AutoDisposeNotifierProvider<
    MainIntelligentListData, List<Intelligent>?>.internal(
  MainIntelligentListData.new,
  name: r'mainIntelligentListDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mainIntelligentListDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MainIntelligentListData = AutoDisposeNotifier<List<Intelligent>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
