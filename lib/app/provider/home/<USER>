import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/main/intelligent_group.dart';
import 'package:text_generation_video/app/repository/service/main_feat_service.dart';

import '../../repository/modals/main/intelligent.dart';
import '../../repository/modals/main/main_banner.dart';

part 'main_features_provider.g.dart';

/// 首页banner
class ManBannerResult {
  final List<MainDataBanner>? list;
  final double? aspectRatio;

  const ManBannerResult({
    this.list,
    this.aspectRatio,
  });

  ManBannerResult copyWith({
    List<MainDataBanner>? list,
    double? aspectRatio,
  }) {
    return ManBannerResult(
      list: list ?? this.list,
      aspectRatio: aspectRatio ?? this.aspectRatio,
    );
  }
}

@riverpod
class MainBannerData extends _$MainBannerData {
  @override
  ManBannerResult? build() {
    loadMainBanner();
    return null;
  }

  // 获取图片宽高比
  Future<double?> _getFirstImageAspectRatio(String? url) async {
    if (url == null || url.isEmpty) return null;
    final Uri uri = Uri.parse(url);
    final HttpClient httpClient = HttpClient();
    final HttpClientRequest request = await httpClient.getUrl(uri);
    final HttpClientResponse response = await request.close();

    if (response.statusCode != HttpStatus.ok) {
      return null;
    }

    final Uint8List bytes = await consolidateHttpClientResponseBytes(response);
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image image = frameInfo.image;

    httpClient.close(); // 关闭 HttpClient

    return image.width / image.height;
  }

  void loadMainBanner() async {
    var bannerResult = await MainFeatService.getMainBannerList();
    if (bannerResult.status == Status.completed) {
      var bannerData = bannerResult.data;
      if (bannerData != null && bannerData.isNotEmpty) {
        var asp = await _getFirstImageAspectRatio(bannerData.first.imgUrl);
        state = ManBannerResult(list: bannerData, aspectRatio: asp);
      }
    }
  }
}

/// ===================首页分组列表=========================///
@riverpod
class MainIntelligentGroupData extends _$MainIntelligentGroupData {
  @override
  List<IntelligentGroup>? build() {
    loadIntelligentGroup();
    return null;
  }

  void loadIntelligentGroup() async {
    var groupResult = await MainFeatService.getIntelligentAppGroupList();
    if (groupResult.status == Status.completed) {
      var groupData = groupResult.data;
      if (groupData != null && groupData.isNotEmpty) {
        state = groupData;
        var groupId = groupData.first.id;
        ref
            .read(mainIntelligentListDataProvider.notifier)
            .loadIntelligent(groupId);
      }
    }
  }
}

@riverpod
class MainIntelligentListData extends _$MainIntelligentListData {
  @override
  List<Intelligent>? build() {
    return null;
  }

  void loadIntelligent(int? groupId) async {
    if (groupId == null) return;
    var result = await MainFeatService.intelligentAppByGroupId(groupId);
    if (result.status == Status.completed) {
      var list = result.data;
      if (list != null && list.isNotEmpty) {
        state = list;
      }
    }
  }
}
