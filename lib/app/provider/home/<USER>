import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'home_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: home_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/29 11:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 11:12
/// @UpdateRemark: 更新说明
class HomeController {
  int index;
  PageController pageController;

  HomeController(this.index, this.pageController);
}

@riverpod
class Home extends _$Home {
  PageController pageController = PageController();

  @override
  HomeController build() {
    ref.onDispose(() {
      pageController.dispose();
    });
    return HomeController(0, pageController);
  }

  void jumpToPage(int index, {bool init = false}) {
    if (init) {
      pageController = PageController(initialPage: index);
    }
    state = HomeController(index, pageController);
    if (!init) {
      pageController.jumpToPage(index);
    }
  }
}

/// 版本号
@riverpod
Future<String?> fetchVersion(Ref ref) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  debugPrint("fetchVersion: ${packageInfo.version}");
  return "版本${packageInfo.version}(${packageInfo.buildNumber})";
}
