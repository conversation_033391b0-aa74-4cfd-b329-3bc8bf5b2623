// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchVersionHash() => r'a1f7016ea3376ba0bed2d5b04ea3d3f07d8b4d0c';

/// 版本号
///
/// Copied from [fetchVersion].
@ProviderFor(fetchVersion)
final fetchVersionProvider = AutoDisposeFutureProvider<String?>.internal(
  fetchVersion,
  name: r'fetchVersionProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fetchVersionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchVersionRef = AutoDisposeFutureProviderRef<String?>;
String _$homeHash() => r'8fc8a5fb21b3985335703036dbd7708debbc6a0d';

/// See also [Home].
@ProviderFor(Home)
final homeProvider = AutoDisposeNotifierProvider<Home, HomeController>.internal(
  Home.new,
  name: r'homeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$homeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Home = AutoDisposeNotifier<HomeController>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
