import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/account/verification_provider.dart';

import '../../../config/global_config.dart';
import '../../../utils/toast_util.dart';
import '../../repository/modals/account/account.dart';
import '../../repository/service/account_service.dart';

part 'auth_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: auth_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/5 17:02
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/5 17:02
/// @UpdateRemark: 更新说明
@riverpod
class Auth extends _$Auth {
  @override
  Account? build() {
    return GlobalConfig.account;
  }

  /// 登录
  Future<Account?> login() async {
    var phone = ref.read(loginPhoneProvider);
    var verCode = ref.read(loginVerCodeProvider);
    if (phone != null &&
        phone.isNotEmpty &&
        verCode != null &&
        verCode.isNotEmpty) {
      SmartDialog.showLoading(msg: "登录中...");
      var result = await AccountService.login(phone, verCode);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        GlobalConfig.account = result.data;
        GlobalConfig.saveProfile();
        state = result.data;
        return result.data;
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
    return null;
  }

  /// 退出登录
  void logout() {
    GlobalConfig.account = null;
    GlobalConfig.saveProfile();
    state = null;
  }
}
