import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../utils/toast_util.dart';
import '../../repository/service/account_service.dart';

part 'verification_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: verification_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/7 11:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/7 11:54
/// @UpdateRemark: 更新说明
@riverpod
class LoginPhone extends _$LoginPhone {
  @override
  String? build() {
    return null;
  }

  /// 设置手机号
  void setPhone(String? name) {
    state = name;
  }
}

@riverpod
class LoginVerCode extends _$LoginVerCode {
  @override
  String? build() {
    return null;
  }

  /// 设置验证码
  void setVerCode(String? code) {
    state = code;
  }
}

@riverpod
class AgreementAgree extends _$AgreementAgree {
  @override
  bool build() {
    return false;
  }

  /// 设置同意隐私政策
  void setAgree(bool agree) {
    state = agree;
  }
}

@riverpod
class Verification extends _$Verification {
  Timer? _timer;

  @override
  int build() {
    return 0;
  }

  void start() {
    int ms = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (ms == 0) {
        timer.cancel();
      } else {
        ms--;
      }
      state = ms;
    });
  }

  void stop() {
    _timer?.cancel();
  }

  /// 获取验证码
  Future<bool> getVerifyCode() async {
    var phone = ref.read(loginPhoneProvider);
    debugPrint("VerificationCodeWidget: $phone");
    if (phone != null && phone.isNotEmpty) {
      SmartDialog.showLoading(msg: "发送中...");
      var result = await AccountService.getPhoneVerifyCode(phone);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        ToastUtil.showToast("验证码已发送");
        return true;
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
        return false;
      }
    }
    return false;
  }
}
