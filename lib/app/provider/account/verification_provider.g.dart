// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$loginPhoneHash() => r'146dc50b0041500fd6d97d2b7808059000d0cc5e';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: verification_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/7 11:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/7 11:54
/// @UpdateRemark: 更新说明
///
/// Copied from [LoginPhone].
@ProviderFor(LoginPhone)
final loginPhoneProvider =
    AutoDisposeNotifierProvider<LoginPhone, String?>.internal(
  LoginPhone.new,
  name: r'loginPhoneProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$loginPhoneHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginPhone = AutoDisposeNotifier<String?>;
String _$loginVerCodeHash() => r'599ac9b175f13c012429f2ac7767fafb4b34a4d7';

/// See also [LoginVerCode].
@ProviderFor(LoginVerCode)
final loginVerCodeProvider =
    AutoDisposeNotifierProvider<LoginVerCode, String?>.internal(
  LoginVerCode.new,
  name: r'loginVerCodeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$loginVerCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginVerCode = AutoDisposeNotifier<String?>;
String _$agreementAgreeHash() => r'14bb40a87e2f435d5a313b34d49ca3d6e2fb8064';

/// See also [AgreementAgree].
@ProviderFor(AgreementAgree)
final agreementAgreeProvider =
    AutoDisposeNotifierProvider<AgreementAgree, bool>.internal(
  AgreementAgree.new,
  name: r'agreementAgreeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$agreementAgreeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AgreementAgree = AutoDisposeNotifier<bool>;
String _$verificationHash() => r'dc3a7e12d02592818772bede90129b2384ea706b';

/// See also [Verification].
@ProviderFor(Verification)
final verificationProvider =
    AutoDisposeNotifierProvider<Verification, int>.internal(
  Verification.new,
  name: r'verificationProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$verificationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Verification = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
