// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'works_record_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$worksListRecordHash() => r'fbfb7b83e64036b99b9ae47ce1a2523a6596da44';

/// See also [WorksListRecord].
@ProviderFor(WorksListRecord)
final worksListRecordProvider =
    AutoDisposeNotifierProvider<WorksListRecord, WorkListResult>.internal(
  WorksListRecord.new,
  name: r'worksListRecordProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$worksListRecordHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WorksListRecord = AutoDisposeNotifier<WorkListResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
