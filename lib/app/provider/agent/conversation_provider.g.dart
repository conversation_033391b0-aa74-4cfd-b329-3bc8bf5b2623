// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'conversation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentConversationHash() =>
    r'2275a0f8fbeeebae6598bb00d516ef66565776eb';

/// See also [CurrentConversation].
@ProviderFor(CurrentConversation)
final currentConversationProvider =
    AutoDisposeNotifierProvider<CurrentConversation, Conversation?>.internal(
  CurrentConversation.new,
  name: r'currentConversationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentConversationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentConversation = AutoDisposeNotifier<Conversation?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
