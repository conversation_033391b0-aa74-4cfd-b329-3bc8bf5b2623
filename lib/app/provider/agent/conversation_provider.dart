import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/agent/conversation.dart';
import 'package:text_generation_video/app/repository/service/agent_service.dart';

import '../../../utils/router_util.dart';
import '../../../utils/toast_util.dart';
import '../../navigation/router.dart';

part 'conversation_provider.g.dart';

// 根据id跳转对应的智能体
@riverpod
class CurrentConversation extends _$CurrentConversation {
  @override
  Conversation? build() {
    return null;
  }

  // 跳转回话
  void jumpConversation(
    BuildContext context,
    String id,
    String? title,
    bool needLogin,
  ) async {
    SmartDialog.showLoading(msg: "加载中...");
    var conversionResult = await AgentService.getCozeConversation(id);
    SmartDialog.dismiss();
    if (conversionResult.status == Status.completed) {
      if (!context.mounted) return;
      state = conversionResult.data;
      var conversationId = conversionResult.data?.conversationId;
      if (conversationId == null || conversationId.isEmpty) return;
      if (needLogin) {
        RouterUtil.checkLogin(context, call: () {
          context.push(
            '/$agentChatPage',
            extra: {
              "botId": id,
              "conversationId": conversationId,
              "title": title
            },
          );
        });
      } else {
        context.push(
          '/$agentChatPage',
          extra: {
            "botId": id,
            "conversationId": conversationId,
            "title": title
          },
        );
      }
    } else {
      ToastUtil.showToast(conversionResult.exception!.getMessage());
    }
  }
}
