import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/agent/work_detail.dart';
import 'package:text_generation_video/app/repository/service/agent_service.dart';
import 'package:ui_widgets/ui_widgets.dart' show LoadState;

part 'works_record_provider.g.dart';

/// 智能体作品列表查询
/// 页长
const int pageSize = 20;

/// 智能体作品列表结果
class WorkListResult {
  final int pageNo;
  final List<WorkDetailList?>? works;
  final LoadState? loadState;

  const WorkListResult({
    this.pageNo = 1,
    this.works,
    this.loadState,
  });

  WorkListResult copyWith({
    int? page,
    List<WorkDetailList?>? works,
    LoadState? loadState,
  }) {
    return WorkListResult(
      pageNo: page ?? pageNo,
      works: works ?? this.works,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class WorksListRecord extends _$WorksListRecord {
  @override
  WorkListResult build() {
    state = const WorkListResult();
    return state;
  }

  /// 加载数据
  void loadData(String? conversationId) async {
    if (conversationId == null || conversationId.isEmpty) return;
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await AgentService.getWorkDetailList(
      conversationId,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      state = state.copyWith(
        works: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore(String? conversationId) async {
    if (conversationId == null || conversationId.isEmpty) return;
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await AgentService.getWorkDetailList(
      conversationId,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          works: [...?state.works, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}