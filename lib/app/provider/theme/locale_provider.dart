import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../config/global_config.dart';
import '../../../utils/prefs_util.dart';

part 'locale_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusai
/// @Package:
/// @ClassName: locale_provider
/// @Description: 国际化语言切换设置，目前支持三种语言（'English', '简体中文'）
/// @Author: frankylee
/// @CreateDate: 2023/5/18 18:11
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/18 18:11
/// @UpdateRemark: 更新说明

enum Language {
  zh,
  en,
}

extension LanguageExtension on Language {
  ///扩展方法，为枚举的value方法
  /// this会作为扩展的方法参数传递，此处的index，是this.index的简写
  String get value => ['English', '简体中文'][index];

  Locale get locale => [
        const Locale('en'),
        const Locale('zh'),
      ][index];
}

@riverpod
class LocaleSetting extends _$LocaleSetting {
  @override
  Locale? build() {
    /// 读取本地保存语言
    Locale? currentLocale;
    var index = PrefsUtil().getInt(PrefsKeys.localeLocal);
    if (index != null) {
      currentLocale = Language.values[index].locale;
      GlobalConfig.localeInfo = currentLocale;
    } else {
      currentLocale = Language.values[1].locale;
    }
    return currentLocale;
  }

  /// 切换语言
  void changeLocale(Language language) {
    PrefsUtil().setInt(PrefsKeys.localeLocal, language.index);
    state = language.locale;
  }
}
