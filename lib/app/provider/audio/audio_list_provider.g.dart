// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$audioListHash() => r'522d39b8a17ea22fae9eb6f38bd6e51e930fee90';

/// 音频列表状态管理Provider
/// 使用AsyncNotifierProvider管理音频列表的增删改查和本地持久化
///
/// Copied from [AudioList].
@ProviderFor(AudioList)
final audioListProvider =
    AutoDisposeAsyncNotifierProvider<AudioList, List<AudioItem>>.internal(
  AudioList.new,
  name: r'audioListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$audioListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AudioList = AutoDisposeAsyncNotifier<List<AudioItem>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
