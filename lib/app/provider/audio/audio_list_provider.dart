import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_item.dart';
import 'package:text_generation_video/app/repository/service/audio_storage_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

part 'audio_list_provider.g.dart';

/// 音频列表状态管理Provider
/// 使用AsyncNotifierProvider管理音频列表的增删改查和本地持久化
@riverpod
class AudioList extends _$AudioList {
  late final AudioStorageService _storageService;

  @override
  Future<List<AudioItem>> build() async {
    _storageService = AudioStorageService();
    
    // 从本地存储加载音频列表
    try {

      final audioList = await _storageService.loadAudioList();
      debugPrint('AudioList Provider: 加载音频列表成功，共${audioList.length}条记录');
      return audioList;
    } catch (e) {
      debugPrint('AudioList Provider: 加载音频列表失败: $e');
      return [];
    }
  }

  /// 添加音频到列表
  Future<void> addAudio(AudioItem audioItem) async {
    try {
      // 更新本地存储
      final success = await _storageService.addAudioItem(audioItem);
      
      if (success) {
        // 更新状态
        final currentList = await future;
        final existingIndex = currentList.indexWhere((item) => item.id == audioItem.id);
        
        if (existingIndex != -1) {
          // 更新现有项
          final updatedList = [...currentList];
          updatedList[existingIndex] = audioItem;
          state = AsyncValue.data(updatedList);
        } else {
          // 添加新项到列表开头
          state = AsyncValue.data([audioItem, ...currentList]);
        }
        
        ToastUtil.showToast('音频添加成功');
        debugPrint('AudioList Provider: 添加音频成功 - ${audioItem.title}');
      } else {
        ToastUtil.showToast('音频添加失败');
        debugPrint('AudioList Provider: 添加音频失败 - ${audioItem.title}');
      }
    } catch (e) {
      debugPrint('AudioList Provider: 添加音频异常: $e');
      ToastUtil.showToast('音频添加失败: $e');
    }
  }

  /// 删除音频列表中的多个音频
  Future<void> removeAudios(List<AudioItem> audioItems) async {
    if (audioItems.isEmpty) return;
    
    try {
      final audioIds = audioItems.map((item) => item.id).toList();
      
      // 更新本地存储
      final success = await _storageService.removeAudioItems(audioIds);
      
      if (success) {
        // 更新状态
        final currentList = await future;
        final filteredList = currentList.where((item) => !audioIds.contains(item.id)).toList();
        state = AsyncValue.data(filteredList);
        
        ToastUtil.showToast('删除${audioItems.length}个音频成功');
        debugPrint('AudioList Provider: 删除音频成功，删除${audioItems.length}个');
      } else {
        ToastUtil.showToast('删除音频失败');
        debugPrint('AudioList Provider: 删除音频失败');
      }
    } catch (e) {
      debugPrint('AudioList Provider: 删除音频异常: $e');
      ToastUtil.showToast('删除音频失败: $e');
    }
  }

  /// 刷新音频列表（重新从本地存储加载）
  Future<void> refresh() async {
    try {
      final audioList = await _storageService.loadAudioList();
      state = AsyncValue.data(audioList);
      debugPrint('AudioList Provider: 刷新音频列表成功，共${audioList.length}条记录');
    } catch (e) {
      debugPrint('AudioList Provider: 刷新音频列表失败: $e');
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}