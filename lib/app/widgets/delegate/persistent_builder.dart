import 'package:flutter/material.dart';

class PersistentBuilder extends SliverPersistentHeaderDelegate {
  final double max;
  final double min;
  final PreferredSizeWidget? child;
  final Widget Function(BuildContext context, double offset)? builder;

  PersistentBuilder({this.max = 50, this.min = 40, this.builder, this.child})
      : assert(max >= min),
        assert(child == null || builder == null),
        assert(child != null || builder != null);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    if (builder != null) {
      return builder!(context, shrinkOffset);
    }
    return child!;
  }

  @override
  double get maxExtent => builder != null ? max : child!.preferredSize.height;

  @override
  double get minExtent => builder != null ? min : child!.preferredSize.height;

  @override
  bool shouldRebuild(covariant PersistentBuilder oldDelegate) =>
      max != oldDelegate.max ||
      min != oldDelegate.min ||
      builder != oldDelegate.builder ||
      child != oldDelegate.child;
}
