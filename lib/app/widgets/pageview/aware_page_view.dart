import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

/// PageView的封装，一般搭配@DragCardPage使用，
/// 可以实现PageView边界滑动时响应页面拖动退出效果
class AwarePageView extends StatefulWidget {
  const AwarePageView({
    super.key,
    this.controller,
    required this.itemCount,
    required this.itemBuilder,
    this.onPageChanged,
    this.dragForward = true,
    this.dragBackward = true,
  });

  final PageController? controller;

  final int itemCount;

  final Widget? Function(BuildContext context, int index) itemBuilder;

  // PageView页面切换回调
  final void Function(int index)? onPageChanged;

  // 开启第一页往前拖动
  final bool dragForward;
  // 开启最后一页往后拖动
  final bool dragBackward;

  @override
  AwarePageViewState createState() => AwarePageViewState();
}

class AwarePageViewState extends State<AwarePageView>
    with SingleTickerProviderStateMixin {
  late PageController _controller;

  AnimationController? _animController;
  Animation<double>? _anim;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? PageController();
    _animController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 300))
      ..addListener(() {
        _controller.jumpTo(_anim!.value);
      });
  }

  @override
  void dispose() {
    _animController?.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _onDragStart(DragStartDetails details) {
    _animController?.stop();
  }

  void _onDragUpdate(DragUpdateDetails details) {
    // 手指滑动距离
    double newOffset = _controller.offset - details.delta.dx;

    // 限制边界
    final maxOffset =
        (widget.itemCount - 1) * MediaQuery.of(context).size.width;
    newOffset = newOffset.clamp(0.0, maxOffset);

    // 页面跟随手指
    _controller.jumpTo(newOffset);
  }

  void _onDragEnd(DragEndDetails details) {
    double pageWidth = MediaQuery.of(context).size.width;
    double currentOffset = _controller.offset;

    // 四舍五入到最近页
    int targetPage = (currentOffset / pageWidth).round();
    targetPage = targetPage.clamp(0, widget.itemCount - 1);

    // 动画过渡到目标页
    _anim = Tween<double>(
      begin: _controller.offset,
      end: targetPage * pageWidth,
    ).animate(CurvedAnimation(parent: _animController!, curve: Curves.easeOut));

    _animController?.forward(from: 0);
  }

  @override
  Widget build(BuildContext context) {
    return RawGestureDetector(
      gestures: {
        _EdgeAwareDragRecognizer:
            GestureRecognizerFactoryWithHandlers<_EdgeAwareDragRecognizer>(
          () => _EdgeAwareDragRecognizer(
            controller: _controller,
            pageCount: widget.itemCount,
            dragForward: widget.dragForward,
            dragBackward: widget.dragBackward,
          ),
          (instance) {
            instance.onStart = _onDragStart;
            instance.onUpdate = _onDragUpdate;
            instance.onEnd = _onDragEnd;
          },
        ),
      },
      behavior: HitTestBehavior.opaque,
      child: PageView.builder(
        itemCount: widget.itemCount,
        controller: _controller,
        physics: const NeverScrollableScrollPhysics(),
        pageSnapping: false,
        onPageChanged: widget.onPageChanged,
        itemBuilder: (context, index) {
          return widget.itemBuilder(context, index);
        },
      ),
    );
  }
}

class _EdgeAwareDragRecognizer extends HorizontalDragGestureRecognizer {
  final PageController controller;
  final int pageCount;

  final bool dragForward;
  final bool dragBackward;

  _EdgeAwareDragRecognizer({
    required this.controller,
    required this.pageCount,
    required this.dragForward,
    required this.dragBackward,
  });

  static const double _eps = 0.0001;

  bool _shouldRelease(double deltaDx) {
    final page = controller.page ?? 0;
    final last = pageCount - 1;
    if (page <= 0.0 + _eps && deltaDx > 0) return dragForward && true; // 第一页右滑
    if (page >= last - _eps && deltaDx < 0) return dragBackward && true; // 最后一页左滑
    return false;
  }

  @override
  void handleEvent(PointerEvent event) {
    if (event is PointerMoveEvent) {
      if (_shouldRelease(event.delta.dx)) {
        // 边界时放手
        resolve(GestureDisposition.rejected);
        return;
      }
    }
    super.handleEvent(event);
  }
}
