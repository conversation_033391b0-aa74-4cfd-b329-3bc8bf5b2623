
import 'package:flutter/material.dart';

/// 自定义胶囊型Tab指示器
class CapsuleTabIndicator extends Decoration {
  final Color color;
  final double height;
  final double bottomMargin;
  final double width;
  final double radius;

  const CapsuleTabIndicator({
    required this.color,
    this.height = 2.0,
    this.bottomMargin = 8.0,
    this.width = 10,
    this.radius = 12,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CapsuleTabIndicatorPainter(
      color: color,
      height: height,
      bottomMargin: bottomMargin,
      width: width,
      radius: radius,
    );
  }
}

class _CapsuleTabIndicatorPainter extends BoxPainter {
  final Color color;
  final double height;
  final double bottomMargin;
  final double width;
  final double radius;

  _CapsuleTabIndicatorPainter({
    required this.color,
    required this.height,
    required this.bottomMargin,
    required this.width,
    required this.radius,
  });

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = Rect.fromLTWH(
      offset.dx + (configuration.size!.width - width) / 2,
      offset.dy + configuration.size!.height - height - bottomMargin,
      width,
      height,
    );
    final RRect rRect = RRect.fromRectAndRadius(rect, Radius.circular(radius));

    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawRRect(rRect, paint);
  }
}
