import 'package:flutter/material.dart';

class HomeTabIndicator extends Decoration {
  const HomeTabIndicator({
    this.insetsGeometry = EdgeInsets.zero,
    this.gradient,
    this.offset = 10,
  });

  final EdgeInsetsGeometry insetsGeometry;

  final Gradient? gradient;

  final double offset;

  @override
  Decoration? lerpFrom(Decoration? a, double t) {
    if (a is HomeTabIndicator) {
      return HomeTabIndicator(
          insetsGeometry:
              EdgeInsetsGeometry.lerp(a.insetsGeometry, insetsGeometry, t)!);
    }
    return super.lerpFrom(a, t);
  }

  @override
  Decoration? lerpTo(Decoration? b, double t) {
    if (b is HomeTabIndicator) {
      return HomeTabIndicator(
          insetsGeometry:
              EdgeInsetsGeometry.lerp(insetsGeometry, b.insetsGeometry, t)!);
    }
    return super.lerpTo(b, t);
  }

  @override
  RoundPainter createBoxPainter([VoidCallback? onChanged]) {
    return RoundPainter(this, onChanged);
  }

  Rect _indicatorRectFor(Rect rect, TextDirection textDirection) {
    final Rect indicator =
        insetsGeometry.resolve(textDirection).deflateRect(rect);
    return Rect.fromLTWH(
      indicator.left,
      indicator.bottom,
      indicator.width,
      indicator.height,
    );
  }

  @override
  Path getClipPath(Rect rect, TextDirection textDirection) {
    return Path()..addRect(_indicatorRectFor(rect, textDirection));
  }
}

class RoundPainter extends BoxPainter {
  RoundPainter(this.decoration, VoidCallback? onChanged) : super(onChanged);

  final HomeTabIndicator decoration;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(configuration.size != null);

    Size mConfiguration = Size(
      configuration.size!.width,
      configuration.size!.height,
    );
    final Rect rect1 = offset & mConfiguration;
    final RRect rRectOut = RRect.fromRectAndRadius(
      rect1,
      const Radius.circular(14),
    );

    Shader shader;
    if (decoration.gradient != null) {
      shader = decoration.gradient!.createShader(rect1);
    } else {
      shader = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Color(0xFF53F9DF),
          Color(0xFF40EFCA),
          Color(0xFF34E8BC),
          Color(0xFF30E6B8),
        ],
        stops: [0, 0.1, 0.3, 1.0],
      ).createShader(rect1);
    }

    canvas.drawRRect(
      rRectOut,
      Paint()
        ..style = PaintingStyle.fill
        ..shader = shader,
    );
  }
}
