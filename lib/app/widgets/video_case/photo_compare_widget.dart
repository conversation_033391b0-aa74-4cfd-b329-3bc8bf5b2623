import 'package:flutter/material.dart';

import '../../../config/icon_address.dart';

/// 老照片修复对比组件：中线+中间按钮，可拖动/点击切换前后对比
class OldPhotoCompare extends StatefulWidget {
  const OldPhotoCompare({
    super.key,
    required this.before,
    required this.after,
    this.width,
    this.height,
    this.initialPosition = 0.5,
    this.borderRadius,
    this.lineWidth = 2,
    this.handleSize = 28,
    this.onChanged,
    this.enableShadow = true,
  }) : assert(initialPosition >= 0 && initialPosition <= 1);

  /// 修复前（左侧起始）
  final Widget before;

  /// 修复后（右侧起始）
  final Widget after;

  final double? width;
  final double? height;

  /// 初始分隔位置（0~1）
  final double initialPosition;

  final BorderRadius? borderRadius;

  /// 分隔线宽度
  final double lineWidth;

  /// 中间按钮直径
  final double handleSize;

  final bool enableShadow;

  /// 拖动时回调：0~1
  final ValueChanged<double>? onChanged;

  @override
  State<OldPhotoCompare> createState() => _OldPhotoCompareState();
}

class _OldPhotoCompareState extends State<OldPhotoCompare>
    with SingleTickerProviderStateMixin {
  late double _pos; // 0~1
  late final AnimationController _ctrl;

  @override
  void initState() {
    super.initState();
    _pos = widget.initialPosition.clamp(0.0, 1.0);
    _ctrl = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 220));
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  void _animateTo(double target) {
    final tween = Tween<double>(begin: _pos, end: target.clamp(0.0, 1.0));
    final anim = tween
        .animate(CurvedAnimation(parent: _ctrl, curve: Curves.easeOutCubic));
    _ctrl
      ..reset()
      ..addListener(() {
        setState(() {
          _pos = anim.value;
          widget.onChanged?.call(_pos);
        });
      })
      ..addStatusListener((s) {
        if (s == AnimationStatus.completed || s == AnimationStatus.dismissed) {
          _ctrl
            ..removeListener(() {})
            ..removeStatusListener((_) {});
        }
      })
      ..forward();
  }

  // 点击按钮时：在 0 -> 0.5 -> 1 -> 0 循环
  void _cycleHandle() {
    const eps = 0.001;
    double target;
    if ((_pos - 0.0).abs() < eps) {
      target = 0.5;
    } else if ((_pos - 0.5).abs() < 0.05) {
      target = 1.0;
    } else if ((_pos - 1.0).abs() < eps) {
      target = 0.0;
    } else {
      // 若在中间任意位置，回到0.5
      target = 0.5;
    }
    _animateTo(target);
  }

  @override
  Widget build(BuildContext context) {
    final radius = widget.borderRadius ?? BorderRadius.circular(12);
    return LayoutBuilder(
      builder: (context, c) {
        final w = widget.width ?? c.maxWidth;
        final h = widget.height ?? (c.maxHeight.isFinite ? c.maxHeight : 220.0);
        final handleRadius = widget.handleSize / 2;

        return ConstrainedBox(
          constraints: BoxConstraints.tightFor(width: w, height: h),
          child: ClipRRect(
            borderRadius: radius,
            child: Stack(
              fit: StackFit.expand,
              children: [
                // 底层：after（修复后）
                widget.after,

                // 顶层左侧裁剪：before（修复前）
                LeftClip(
                  clipXFraction: _pos,
                  child: widget.before,
                ),

                // 整体手势：点击任意位置移动滑块
                Positioned.fill(
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTapDown: (d) {
                      final localX = d.localPosition.dx;
                      final target = (localX / w).clamp(0.0, 1.0);
                      _animateTo(target);
                    },
                    onHorizontalDragUpdate: (d) {
                      setState(() {
                        _pos = (_pos + d.primaryDelta! / w).clamp(0.0, 1.0);
                        widget.onChanged?.call(_pos);
                      });
                    },
                  ),
                ),

                // 分隔线
                Positioned(
                  left: (_pos * w) - (widget.lineWidth / 2),
                  top: 0,
                  bottom: 0,
                  child: IgnorePointer(
                    child: Container(
                      width: widget.lineWidth,
                      color: Colors.white,
                    ),
                  ),
                ),

                // 中间按钮（可拖动/可点按）
                Positioned(
                  left: (_pos * w) - handleRadius,
                  top: (h / 2) - handleRadius,
                  child: _Handle(
                    size: widget.handleSize,
                    elevation: widget.enableShadow ? 6 : 0,
                    onDragUpdate: (dx) {
                      setState(() {
                        _pos = (_pos + dx / w).clamp(0.0, 1.0);
                        widget.onChanged?.call(_pos);
                      });
                    },
                    onTap: _cycleHandle,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 左侧裁剪器：显示 [0, clipX] 的内容
class LeftClip extends StatelessWidget {
  const LeftClip({super.key, required this.clipXFraction, required this.child});

  final double clipXFraction;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, c) {
      final w = c.maxWidth;
      return ClipPath(
        clipper: _LeftClipper(clipX: (clipXFraction * w).clamp(0, w)),
        child: child,
      );
    });
  }
}

class _LeftClipper extends CustomClipper<Path> {
  _LeftClipper({required this.clipX});

  final double clipX;

  @override
  Path getClip(Size size) {
    final x = clipX.clamp(0.0, size.width);
    final path = Path()
      ..addRect(Rect.fromLTWH(0, 0, x, size.height))
      ..close();
    return path;
  }

  @override
  bool shouldReclip(covariant _LeftClipper oldClipper) =>
      oldClipper.clipX != clipX;
}

/// 中间按钮
class _Handle extends StatefulWidget {
  const _Handle({
    required this.size,
    required this.onDragUpdate,
    required this.onTap,
    this.elevation = 6,
  });

  final double size;
  final double elevation;
  final ValueChanged<double> onDragUpdate; // dx
  final VoidCallback onTap;

  @override
  State<_Handle> createState() => _HandleState();
}

class _HandleState extends State<_Handle> {

  @override
  Widget build(BuildContext context) {
    final s = widget.size;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: widget.onTap,
      onHorizontalDragUpdate: (d) {
        widget.onDragUpdate(d.delta.dx);
      },
      child: Container(
        width: s,
        height: s,
        alignment: Alignment.center,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(photoCompareIcon,fit: BoxFit.contain)
          ],
        ),
      ),
    );
  }
}