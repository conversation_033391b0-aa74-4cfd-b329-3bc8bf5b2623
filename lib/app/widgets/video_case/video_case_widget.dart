import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/text_to_video/video_case_provider.dart';
import 'package:text_generation_video/app/repository/modals/video/video_case.dart';
import 'package:text_generation_video/app/widgets/video_case/dialog/video_case_dialog.dart';

import '../../../config/icon_address.dart';

class VideoCaseWidget extends ConsumerStatefulWidget {
  const VideoCaseWidget({super.key, required this.caseType});

  final int caseType;

  @override
  VideoCaseWidgetState createState() => VideoCaseWidgetState();
}

class VideoCaseWidgetState extends ConsumerState<VideoCaseWidget> {
  // 案例列表
  Widget _buildCaseList(List<VideoCase> caseList) {
    var itemWidth = 85.81.w;
    var itemHeight = 115 * itemWidth / 85.81;
    return SizedBox(
      height: itemHeight,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          var caseItem = caseList[index];
          return InkWell(
            onTap: () {
              VideoCaseDialog.showVideoCaseDialog(context, caseItem);
            },
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: "${caseItem.cover}",
                    width: itemWidth,
                    height: itemHeight,
                    fit: BoxFit.cover,
                    errorWidget: (_, o, s) {
                      return Container(
                        width: itemWidth,
                        height: itemHeight,
                        color: Colors.grey.shade800,
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 6),
                  child: Text(
                    "${caseItem.caseName}",
                    style: const TextStyle(
                      fontSize: 11,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        separatorBuilder: (context, index) {
          return const SizedBox(width: 10);
        },
        itemCount: caseList.length,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ref.watch(fetchPopularVideoCaseProvider(widget.caseType)).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return const SizedBox();
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Image.asset(textToVideoCaseIcon, width: 15, height: 15),
                const SizedBox(width: 2),
                const Text(
                  "热门案例",
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            const Text(
              "点击下方素材，查看创意描述示例",
              style: TextStyle(
                fontSize: 11,
                color: Color(0xFF6F737A),
              ),
            ),
            const SizedBox(height: 11),
            _buildCaseList(data),
          ],
        );
      },
      error: (o, s) {
        return const SizedBox();
      },
      loading: () {
        return const SizedBox();
      },
    );
  }
}
