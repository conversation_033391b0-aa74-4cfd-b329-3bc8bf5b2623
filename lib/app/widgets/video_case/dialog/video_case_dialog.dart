import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/text_to_video/text_to_video_provider.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:video_player/video_player.dart';

import '../../../../config/icon_address.dart';
import '../../../repository/modals/video/video_case.dart';

// 热门案例显示
class VideoCaseDialog {
  static void showVideoCaseDialog(BuildContext context, VideoCase videoCase) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return VideoCaseDialogWidget(videoCase: videoCase);
      },
    );
  }
}

class VideoCaseDialogWidget extends ConsumerWidget {
  const VideoCaseDialogWidget({super.key, required this.videoCase});

  final VideoCase videoCase;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var caseImgList = [];
    if (videoCase.caseImg1 != null && videoCase.caseImg1!.isNotEmpty) {
      caseImgList.add(videoCase.caseImg1);
    }
    if (videoCase.caseImg2 != null && videoCase.caseImg2!.isNotEmpty) {
      caseImgList.add(videoCase.caseImg2);
    }
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 12),
          const Row(
            children: [
              Text(
                "创意描述词",
                style: TextStyle(
                  fontSize: 15,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          IntrinsicHeight(
            child: Row(
              children: [
                const VerticalDivider(
                  width: 0.6,
                  color: Color(0xFF565656),
                ),
                const SizedBox(width: 17),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (caseImgList.isNotEmpty)
                        Container(
                          height: 79,
                          margin: const EdgeInsets.only(bottom: 12),
                          child: ListView.separated(
                            scrollDirection: Axis.horizontal,
                            itemCount: caseImgList.length,
                            itemBuilder: (BuildContext context, int index) {
                              var imgUrl = caseImgList[index];
                              return CachedNetworkImage(
                                imageUrl: imgUrl,
                                width: 79,
                                height: 79,
                                fit: BoxFit.cover,
                                errorWidget: (_, o, s) {
                                  return Container(
                                    width: 79,
                                    height: 79,
                                    color: Colors.grey,
                                  );
                                },
                              );
                            },
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return const SizedBox(width: 10);
                            },
                          ),
                        ),
                      if (videoCase.casePrompt != null)
                        Text(
                          "${videoCase.casePrompt}",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF8A8D93),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          const Row(
            children: [
              Text(
                "成片效果",
                style: TextStyle(
                  fontSize: 15,
                  color: Color(0xFFFFFFFF),
                ),
              )
            ],
          ),
          const SizedBox(height: 10),
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF000000),
              borderRadius: BorderRadius.circular(18),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 267,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: VideoBox(
                    maxWidth: MediaQuery.sizeOf(context).width - 52,
                    maxHeight: 380,
                    ratio: videoCase.ratio,
                    videoUrl: videoCase.caseVideoUrl,
                  ),
                ),
                GradientButton(
                  onPress: () {
                    context.pop();
                    ref
                        .read(videoEditTextProvider.notifier)
                        .setText(videoCase.casePrompt);
                    ref
                        .read(videoScaleParamProvider.notifier)
                        .caseValue(videoCase);
                    ref
                        .read(videoDurationParamProvider.notifier)
                        .caseValue(videoCase);
                    ref
                        .read(videoClarityParamProvider.notifier)
                        .caseValue(videoCase);
                  },
                  radius: 16,
                  shadow: false,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  gradient: const LinearGradient(
                    colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                  ),
                  child: const Text(
                    "去试试",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF18161A),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// 视频显示区
class VideoBox extends StatefulWidget {
  final double maxWidth;
  final double maxHeight;
  final String? ratio; // 例如 "16:9" 或 "3:4"
  final String? videoUrl;

  const VideoBox({
    super.key,
    required this.maxWidth,
    required this.maxHeight,
    required this.ratio,
    required this.videoUrl,
  });

  @override
  VideoBoxState createState() => VideoBoxState();
}

class VideoBoxState extends State<VideoBox> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();
    debugPrint("VideoBoxState: ${widget.videoUrl}");
    var videoUrl = widget.videoUrl ?? '';
    var uri = Uri.parse(videoUrl);
    _controller = VideoPlayerController.networkUrl(uri)
      ..initialize().then(
        (_) {
          _controller.play();
        },
      ).catchError(
        (e) {
          debugPrint('catchError: $e');
        },
      );
    _controller.addListener(handle);
  }

  @override
  void dispose() {
    _controller.removeListener(handle);
    _controller.dispose();
    super.dispose();
  }

  void handle() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    // 解析比例
    final parts = (widget.ratio ?? "3:4").split(':');
    final w = double.tryParse(parts[0]) ?? 16;
    final h = double.tryParse(parts[1]) ?? 9;
    final aspectRatio = w / h;

    return LayoutBuilder(
      builder: (context, constraints) {
        // 实际能用的最大宽高
        final maxW = constraints.maxWidth == double.infinity
            ? widget.maxWidth
            : constraints.maxWidth.clamp(0, widget.maxWidth).toDouble();
        final maxH = constraints.maxHeight == double.infinity
            ? widget.maxHeight
            : constraints.maxHeight.clamp(0, widget.maxHeight).toDouble();

        // 计算合适的宽高
        double width = maxW;
        double height = width / aspectRatio;
        if (height > maxH) {
          height = maxH;
          width = height * aspectRatio;
        }

        return Center(
          child: SizedBox(
            width: width,
            height: height,
            child: AspectRatio(
              aspectRatio: aspectRatio,
              child: _controller.value.isInitialized
                  ? Stack(
                      alignment: Alignment.center,
                      children: [
                        VideoPlayer(_controller),
                        if (!_controller.value.isPlaying)
                          InkWell(
                            onTap: () {
                              _controller.play();
                            },
                            child: Image.asset(
                              pauseIcon,
                              width: 40,
                              height: 40,
                              fit: BoxFit.contain,
                            ),
                          ),
                      ],
                    )
                  : const Center(
                      child: CircularProgressIndicator(
                        color: Colors.green,
                        strokeWidth: 1,
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }
}
