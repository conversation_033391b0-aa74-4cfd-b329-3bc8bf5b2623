import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../config/icon_address.dart';

class ConsumptionDisplayWidget extends ConsumerWidget {
  const ConsumptionDisplayWidget({super.key, required this.consumption});

  final int consumption;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Image.asset(
                consumingInspirationIcon,
                width: 13,
                height: 14,
              ),
              const SizedBox(width: 6),
              Text(
                "本次消耗$consumption灵感值",
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ),
          const Row(
            children: [
              Text(
                "剩余150灵感值",
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFFFFFFFF),
                ),
              ),
              SizedBox(width: 6),
              Icon(
                Icons.arrow_forward_ios_rounded,
                size: 10,
                color: Colors.white,
              ),
            ],
          )
        ],
      ),
    );
  }

}