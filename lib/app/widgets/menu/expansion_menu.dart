import 'package:flutter/material.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: jewelry_aigc
/// @Package: common.widgets.menu
/// @ClassName: expansion_menu
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/2/1 14:22
/// @UpdateUser: frankylee
/// @UpdateData: 2024/2/1 14:22
/// @UpdateRemark: 更新说明

/// 菜单
class LeftMenu {
  int index;
  String name;
  String icon;
  List<LeftSubMenu> children;

  LeftMenu(this.index, this.name, this.icon, this.children);
}

/// 子菜单
class LeftSubMenu {
  int index;
  int parentIndex;
  String name;
  String desc;

  LeftSubMenu(this.index, this.parentIndex, this.name, this.desc);
}

class ExpansionMenu extends StatefulWidget {
  const ExpansionMenu({
    super.key,
    required this.menuList,
    required this.onMenuChange,
    this.parentIndex = 0,
    this.childrenIndex = 0,
  });

  final List<LeftMenu> menuList;

  final Function(int parentIndex, int index) onMenuChange;

  final int? parentIndex;
  final int? childrenIndex;

  @override
  ExpansionMenuState createState() => ExpansionMenuState();
}

class ExpansionMenuState extends State<ExpansionMenu> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _buildMenu(),
      ),
    );
  }

  /// 构造一级菜单
  List<Widget> _buildMenu() {
    List<Widget> menu = [];
    for (var i = 0; i < widget.menuList.length; i++) {
      var menuItem = widget.menuList[i];
      menu.add(
        ExpansionTile(
          shape: const Border(),
          initiallyExpanded: true,
          tilePadding: const EdgeInsets.only(left: 37, right: 14),
          title: Row(
            children: [
              Image.asset(
                menuItem.icon,
                width: 20,
                height: 20,
              ),
              const Padding(padding: EdgeInsets.only(right: 6)),
              Text(
                menuItem.name,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          childrenPadding: const EdgeInsets.symmetric(
            horizontal: 14,
          ),
          //children padding
          children: _buildSubMenu(menuItem),
        ),
      );
    }
    return menu;
  }

  /// 构造二级级菜单
  List<Widget> _buildSubMenu(LeftMenu leftMenu) {
    List<Widget> menuItemChildren = [];
    for (var k = 0; k < leftMenu.children.length; k++) {
      var childrenItem = leftMenu.children[k];
      var selected = widget.parentIndex == leftMenu.index &&
          widget.childrenIndex == childrenItem.index;
      menuItemChildren.add(
        DecoratedBox(
          decoration: BoxDecoration(
            color: selected ? const Color(0xFF1C63F2).withAlpha(15) : null,
            borderRadius: BorderRadius.circular(6),
          ),
          child: ListTile(
            selected: selected,
            selectedColor: const Color(0xFF1C63F2),
            contentPadding: const EdgeInsets.only(left: 48),
            title: Text(
              childrenItem.name,
              style: const TextStyle(fontSize: 14),
            ),
            onTap: () {
              widget.onMenuChange(leftMenu.index, childrenItem.index);
            },
          ),
        ),
      );
    }
    return menuItemChildren;
  }
}
