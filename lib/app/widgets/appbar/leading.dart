import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../config/icon_address.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: leading
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 10:53
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 10:53
/// @UpdateRemark: 更新说明
class Leading extends StatelessWidget {
  const Leading({
    super.key,
    this.color,
    this.onBack,
    this.leadingWidget,
  });

  final Color? color;

  final Function()? onBack;

  final Widget? leadingWidget;

  @override
  Widget build(BuildContext context) {
    if (!Navigator.canPop(context)) {
      return leadingWidget ?? Container();
    }
    return InkWell(
      onTap: () {
        if (onBack != null) {
          onBack?.call();
        }
        Navigator.pop(context);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            arrowLeft,
            width: 22.w,
            height: 22.w,
            color: color,
          ),
        ],
      ),
    );
  }
}
