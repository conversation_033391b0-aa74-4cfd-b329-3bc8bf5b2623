import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/text_to_video/text_to_video_provider.dart';

// 视频比例选择
class VideoScaleDialog {
  static void showVideoScaleDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return const VideoScaleWidget();
      },
    );
  }
}

class VideoScaleWidget extends ConsumerWidget {
  const VideoScaleWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var select = ref.watch(videoScaleParamProvider);
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 13),
          const Text(
            "选择视频比例",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFFFFFFFF),
            ),
          ),
          const SizedBox(height: 26),
          GridView(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 107 / 107,
            ),
            children: scaleParamList.map(
              (e) {
                var isSelect = e.scale == select.scale;
                return InkWell(
                  onTap: () {
                    ref.read(videoScaleParamProvider.notifier).select(e);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelect
                          ? const Color(0xFF222123)
                          : const Color(0xFF39373B),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelect
                            ? const Color(0xFF30E6B8)
                            : const Color(0xFF39373B),
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: e.width,
                          height: e.height,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isSelect
                                  ? const Color(0xFF30E6B8)
                                  : Colors.white,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        SizedBox(height: e.margin),
                        Text(
                          e.scale,
                          style: TextStyle(
                            fontSize: 14,
                            color: isSelect
                                ? const Color(0xFF30E6B8)
                                : const Color(0xFFFFFFFF),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ).toList(),
          ),
        ],
      ),
    );
  }
}

// 视频时长选择
class VideoDurationDialog {
  static void showVideoDurationDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return const VideoDurationWidget();
      },
    );
  }
}

class VideoDurationWidget extends ConsumerWidget {
  const VideoDurationWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var select = ref.watch(videoDurationParamProvider);
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 13),
          const Text(
            "选择视频时长",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFFFFFFFF),
            ),
          ),
          const SizedBox(height: 26),
          GridView(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 165 / 51,
            ),
            children: durationParamList.map(
              (e) {
                var isSelect = e.duration == select.duration;
                return InkWell(
                  onTap: () {
                    ref.read(videoDurationParamProvider.notifier).select(e);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelect
                          ? const Color(0xFF222123)
                          : const Color(0xFF39373B),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelect
                            ? const Color(0xFF30E6B8)
                            : const Color(0xFF39373B),
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          e.duration,
                          style: TextStyle(
                            fontSize: 14,
                            color: isSelect
                                ? const Color(0xFF30E6B8)
                                : const Color(0xFFFFFFFF),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ).toList(),
          ),
        ],
      ),
    );
  }
}

// 视频模式选择
class VideoClarityDialog {
  static void showVideoClarityDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return const VideoClarityWidget();
      },
    );
  }
}

class VideoClarityWidget extends ConsumerWidget {
  const VideoClarityWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var select = ref.watch(videoClarityParamProvider);
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 13),
          const Text(
            "选择生成模式",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFFFFFFFF),
            ),
          ),
          const SizedBox(height: 26),
          GridView(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 107 / 51,
            ),
            children: clarityParamList.map(
              (e) {
                var isSelect = e.value == select.value;
                return InkWell(
                  onTap: () {
                    ref.read(videoClarityParamProvider.notifier).select(e);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelect
                          ? const Color(0xFF222123)
                          : const Color(0xFF39373B),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelect
                            ? const Color(0xFF30E6B8)
                            : const Color(0xFF39373B),
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          e.clarity,
                          style: TextStyle(
                            fontSize: 14,
                            color: isSelect
                                ? const Color(0xFF30E6B8)
                                : const Color(0xFFFFFFFF),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ).toList(),
          ),
        ],
      ),
    );
  }
}
