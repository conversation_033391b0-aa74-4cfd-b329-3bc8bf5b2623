import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/creation/creation_provider.dart';
import 'package:text_generation_video/config/icon_address.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.creation.widget
/// @ClassName: creation_type_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/1 15:27
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/1 15:27
/// @UpdateRemark: 更新说明
class CreationTypeWidget extends ConsumerWidget {
  const CreationTypeWidget({super.key});

  Widget _buildItem(WidgetRef ref, VideoStyle videoStyle) {
    var currentStyle = ref.watch(currentVideoStyleProvider);
    bool select = videoStyle.value == currentStyle.value;
    Widget selectTag = Container();
    if (select) {
      selectTag = Image.asset(
        videoStyleSelect,
        width: 22.w,
        height: 16.h,
      );
    }
    return InkWell(
      onTap: () {
        ref
            .read(currentVideoStyleProvider.notifier)
            .selectVideoStyle(videoStyle);
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4.r),
        child: Container(
          width: 77.w,
          height: 77.w,
          decoration: BoxDecoration(
            border: Border.all(
              color: select ? const Color(0xFF00CF6F) : Colors.transparent,
              width: select ? 2 : 0,
            ),
          ),
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Image.network(
                videoStyle.img,
                fit: BoxFit.contain,
                width: 77.w,
                height: 77.w,
              ),
              Container(
                width: 77.w,
                height: 22.5.h,
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  color: Color(0x43000000),
                ),
                child: Text(
                  videoStyle.name,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              Positioned(
                top: -2,
                right: -2,
                child: selectTag,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverPadding(
      padding: EdgeInsets.fromLTRB(17.w, 12.h, 17.w, 12.h),
      sliver: SliverGrid.builder(
        itemCount: videoStyleList.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          mainAxisSpacing: 11.h,
          crossAxisSpacing: 11.w,
        ),
        itemBuilder: (BuildContext context, int index) {
          return _buildItem(ref, videoStyleList[index]);
        },
      ),
    );
  }
}
