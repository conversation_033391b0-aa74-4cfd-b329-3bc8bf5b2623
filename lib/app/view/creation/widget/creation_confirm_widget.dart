import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/icon_address.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.creation.widget
/// @ClassName: creation_confirm_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/1 15:27
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/1 15:27
/// @UpdateRemark: 更新说明
class CreationConfirmWidget extends StatelessWidget {
  const CreationConfirmWidget({
    super.key,
    required this.onPrevious,
    required this.onNext,
  });

  final Function() onPrevious;
  final Function() onNext;

  /// 上一步按钮
  Widget _buildPrevious(BuildContext context) {
    return SizedBox(
      width: 87.w,
      height: 47.h,
      child: GradientButton(
        onPress: onPrevious,
        radius: 12.r,
        border: Border.all(color: Colors.black),
        padding: EdgeInsets.symmetric(vertical: 11.h),
        gradient: const LinearGradient(
          colors: [Color(0xFFFFFFFF), Color(0xFFFFFFFF)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        child: Image.asset(
          previousIcon,
          width: 24.w,
          height: 20.h,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  /// 下一步按钮
  Widget _buildNext(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: 47.h,
        child: GradientButton(
          onPress: onNext,
          radius: 12.r,
          margin: EdgeInsets.only(left: 14.w),
          gradient: const LinearGradient(
            colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          child: Text(
            "下一步",
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// 音色选择设置
  Widget _buildTimbre() {
    return Container(
      padding: EdgeInsets.fromLTRB(13.w, 18.h, 13.w, 12.h),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F8FA),
        gradient: const LinearGradient(
          colors: [Color(0xFFF2F5FF), Color(0xFFEBF5FD)],
        ),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 48.w,
                height: 48.w,
                color: Colors.grey,
                margin: EdgeInsets.only(left: 8.w, right: 9.w),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "知妙_多情感",
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      "细腻自然，情感到位",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF898989),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          Padding(padding: EdgeInsets.only(bottom: 23.h)),
          Row(
            children: [
              _buildSelectItem(listenIcon, "声音试听", () {}),
              Padding(padding: EdgeInsets.only(right: 10.w)),
              _buildSelectItem(soundIcon, "声音设置", () {}),
              Padding(padding: EdgeInsets.only(right: 10.w)),
              _buildSelectItem(anchorIcon, "切换主播", () {}),
            ],
          )
        ],
      ),
    );
  }

  /// 按钮item
  Widget _buildSelectItem(String icon, String text, Function() onPress) {
    return Expanded(
      child: GradientButton(
        onPress: onPress,
        radius: 8.r,
        padding: EdgeInsets.symmetric(vertical: 9.h),
        gradient: const LinearGradient(
          colors: [Color(0xFFFFFFFF), Color(0xFFFFFFFF)],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              icon,
              width: 16.w,
              height: 15.h,
              fit: BoxFit.contain,
            ),
            Padding(padding: EdgeInsets.only(right: 4.w)),
            Text(
              text,
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF333333),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        margin: EdgeInsets.only(top: 40.h, left: 17.w, right: 17.w),
        child: Column(
          children: [
            Row(
              children: [
                Image.asset(
                  timbreIcon,
                  width: 24.w,
                  height: 25.h,
                  fit: BoxFit.contain,
                ),
                Padding(padding: EdgeInsets.only(right: 6.w)),
                Text(
                  "选择音色",
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            Padding(padding: EdgeInsets.only(bottom: 10.h)),
            _buildTimbre(),
            Padding(padding: EdgeInsets.only(bottom: 185.h)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 18.w),
              child: Row(
                children: [_buildPrevious(context), _buildNext(context)],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
