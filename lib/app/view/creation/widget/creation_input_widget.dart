import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../config/icon_address.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.creation.widget
/// @ClassName: creation_input_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/1 15:24
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/1 15:24
/// @UpdateRemark: 更新说明
class CreationInputWidget extends StatefulWidget {
  const CreationInputWidget({
    super.key,
    required this.controller,
  });

  final TextEditingController controller;

  @override
  CreationInputWidgetState createState() => CreationInputWidgetState();
}

class CreationInputWidgetState extends State<CreationInputWidget> {
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(handle);
  }

  @override
  void dispose() {
    super.dispose();
    widget.controller.removeListener(handle);
  }

  void handle() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        margin: EdgeInsets.only(top: 10.h, left: 17.w, right: 17.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "视频提示内容",
              style: TextStyle(
                fontSize: 17.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            Padding(padding: EdgeInsets.only(bottom: 12.h)),
            Container(
              padding: EdgeInsets.fromLTRB(15.w, 2.h, 15.w, 15.h),
              decoration: BoxDecoration(
                color: const Color(0xFFF6F8FA),
                borderRadius: BorderRadius.circular(10.r),
                border: Border.all(
                  color: const Color(0xFF969696),
                ),
              ),
              child: TextField(
                onTapOutside: (event) {
                  _focusNode.unfocus();
                },
                focusNode: _focusNode,
                controller: widget.controller,
                maxLength: 400,
                maxLines: 8,
                style: TextStyle(fontSize: 14.sp, color: Colors.black),
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: "请输入视频创作内容及要求，AI会根据您的内容和要求制作图片并自动生成有声视频",
                  hintStyle: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFFB7B7B8),
                  ),
                  counter: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      InkWell(
                        onTap: () {
                          widget.controller.clear();
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 6.w),
                          child: Image.asset(
                            delete,
                            width: 12.w,
                            height: 15.h,
                          ),
                        ),
                      ),
                      Text(
                        "${widget.controller.text.length}/400",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFFB7B7B8),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
