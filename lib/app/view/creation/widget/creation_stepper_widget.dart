import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_widgets/ui_widgets.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.creation.widget
/// @ClassName: creation_stepper_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/1 16:51
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/1 16:51
/// @UpdateRemark: 更新说明
class CreationStepperWidget extends StatefulWidget {
  const CreationStepperWidget({
    super.key,
    required this.pageController,
  });

  final PageController pageController;

  @override
  CreationStepperWidgetState createState() => CreationStepperWidgetState();
}

class CreationStepperWidgetState extends State<CreationStepperWidget> {
  double? select = 0;

  @override
  void initState() {
    widget.pageController.addListener(_handle);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    widget.pageController.removeListener(_handle);
  }

  void _handle() {
    setState(() {
      select = widget.pageController.page;
    });
  }

  Widget _buildSelect(
    int index,
    String value,
    String title, {
    bool showStepper = true,
  }) {
    Widget stepperLine = Container();
    if (showStepper) {
      stepperLine = Container(
        width: 52.w,
        height: 2.h,
        margin: EdgeInsets.only(left: 4.w),
        child: LDashedLine(
          dashedHeight: 2.h,
          dashedWidth: 3.w,
          dashedColor: const Color(0xFFB5B5B5),
        ),
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 26.w,
              height: 26.w,
              margin: EdgeInsets.only(left: 7.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: select == index ? Colors.black : Colors.white,
                borderRadius: BorderRadius.circular(13.w),
                border: Border.all(
                  color: const Color(0xFF333333),
                ),
              ),
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 16.sp,
                  color:
                      select == index ? Colors.white : const Color(0xFF333333),
                ),
              ),
            ),
            stepperLine,
          ],
        ),
        Padding(padding: EdgeInsets.only(bottom: 4.h)),
        Text(
          title,
          style: TextStyle(
            fontSize: 10.sp,
            color: select == index ? Colors.black : const Color(0xFF666666),
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 312.w,
      margin: EdgeInsets.only(top: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildSelect(0, "1", "输入内容"),
          _buildSelect(1, "2", "选择音色"),
          _buildSelect(2, "3", "内容确认"),
          _buildSelect(3, "4", "图片类型", showStepper: false),
        ],
      ),
    );
  }
}
