import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/creation/creation_provider.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../repository/modals/voice/voice.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.creation.widget
/// @ClassName: creation_timbre_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/1 15:27
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/1 15:27
/// @UpdateRemark: 更新说明
class CreationTimbreWidget extends ConsumerWidget {
  const CreationTimbreWidget({
    super.key,
    this.onPrevious,
    this.onNext,
  });

  final Function()? onPrevious;
  final Function()? onNext;

  /// 配音选择bottom sheet
  void _soundSetting(BuildContext context, WidgetRef ref, List<Voice> data) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          maxChildSize: 0.6,
          snap: true,
          expand: false,
          builder: (context, controller) {
            return Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 14.h, bottom: 4.h),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "配音主播",
                              style: TextStyle(
                                fontSize: 17.sp,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                        Positioned(
                          right: 24.w,
                          child: InkWell(
                            onTap: () {
                              context.pop();
                            },
                            child: Icon(
                              Icons.keyboard_arrow_down_rounded,
                              size: 20.r,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.separated(
                      controller: controller,
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        return _buildSoundSettingItem(
                          context,
                          ref,
                          data[index],
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return Divider(
                          indent: 26.w,
                          endIndent: 25.w,
                          color: const Color(0xFFDBDBDB),
                          height: 0.8,
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// 声音设置item
  Widget _buildSoundSettingItem(
    BuildContext context,
    WidgetRef ref,
    Voice voice,
  ) {
    return InkWell(
      onTap: () {
        ref.read(currentVoiceProvider.notifier).selectVoice(voice);
        context.pop();
      },
      child: Container(
        padding: EdgeInsets.fromLTRB(21.w, 20.h, 22.w, 22.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  margin: EdgeInsets.only(right: 9.w),
                  child: Image.asset(
                    voiceLogo,
                    width: 44.w,
                    height: 44.w,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${voice.desc}",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      "大气稳重，音质沉稳",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF999999),
                      ),
                    ),
                  ],
                )
              ],
            ),
            Image.asset(
              anchorVoiceIcon,
              width: 32.w,
              height: 32.w,
            ),
          ],
        ),
      ),
    );
  }

  /// 音色选择设置
  Widget _buildTimbre(BuildContext context, WidgetRef ref, List<Voice> data) {
    var currentVoice = ref.watch(currentVoiceProvider) ?? data.first;
    return Container(
      padding: EdgeInsets.fromLTRB(13.w, 18.h, 13.w, 12.h),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F8FA),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                margin: EdgeInsets.only(left: 8.w, right: 9.w),
                child: Image.asset(
                  voiceLogo,
                  width: 48.w,
                  height: 48.w,
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${currentVoice.desc}-${currentVoice.gender}",
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      "细腻自然，情感到位",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF898989),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          Padding(padding: EdgeInsets.only(bottom: 15.h)),
          Row(
            children: [
              _buildSelectItem(anchorIcon, "选择配音", () {
                _soundSetting(context, ref, data);
              }),
            ],
          )
        ],
      ),
    );
  }

  /// 按钮item
  Widget _buildSelectItem(String icon, String text, Function() onPress) {
    return Expanded(
      child: GradientButton(
        margin: EdgeInsets.symmetric(horizontal: 23.w),
        onPress: onPress,
        radius: 8.r,
        padding: EdgeInsets.symmetric(vertical: 9.h),
        gradient: const LinearGradient(
          colors: [Color(0xFFFFFFFF), Color(0xFFFFFFFF)],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              icon,
              width: 18.w,
              height: 18.h,
              fit: BoxFit.contain,
            ),
            Padding(padding: EdgeInsets.only(right: 4.w)),
            Text(
              text,
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF000000),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SliverToBoxAdapter(
      child: ref.watch(fetchVoiceListProvider).when(
        data: (data) {
          if (data == null || data.isEmpty) {
            return Container();
          }
          return Container(
            margin: EdgeInsets.only(top: 10.h, left: 17.w, right: 17.w),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "文本配音",
                      style: TextStyle(
                        fontSize: 17.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
                Padding(padding: EdgeInsets.only(bottom: 12.h)),
                _buildTimbre(context, ref, data),
              ],
            ),
          );
        },
        error: (o, s) {
          return Container();
        },
        loading: () {
          return Container();
        },
      ),
    );
  }
}
