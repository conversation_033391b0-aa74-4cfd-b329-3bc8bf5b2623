import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

class MemberCheckDialog {
  // member: true表示是非会员
  static void showCheckDialog(Function onPress, bool member) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: true,
      tag: "member_check_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 313.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 22.h, bottom: 29.h),
                    child: Text(
                      member ? '请先开通会员' : '请先续费会员',
                      style: const TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Text(
                    "抱歉，本服务仅限会员专享",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF939393),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 30.h)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "member_check_dialog");
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FD),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              "关闭",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "member_check_dialog");
                            onPress();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                              color: const Color(0xFF342A21),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              member ? '开通会员' : '续费会员',
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFEDCAF),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                    ],
                  ),
                  const SizedBox(height: 15),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
