import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

class ConfirmCommitDialog {
  static Future<bool?> confirmCommit(int powerItem) async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "confirm_creation_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 313.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 22.h, bottom: 29.h),
                    child: const Text(
                      "确认是否提交",
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Text(
                    "本次消耗$powerItem算力，请确认是否提交",
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF939393),
                    ),
                  ),
                  const SizedBox(height: 31),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "confirm_creation_dialog",
                              result: false,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FD),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              "关闭",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "confirm_creation_dialog",
                              result: true,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFF00CD6E),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              "确认提交",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                    ],
                  ),
                  const SizedBox(height: 15),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // 算力不足提示
  static void powerLessCommit(Function onPress, {int? powerItem}) async {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "power_less_dialog",
      builder: (context) {
        String txt = "";
        if (powerItem == null) {
          txt = "（续费会员，免费送算力）";
        } else {
          txt = "（本次消耗$powerItem算力）";
        }
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 313.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 22.h, bottom: 29.h),
                    child: const Text(
                      "算力不足",
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const Text(
                    "抱歉，您的算力不足",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF939393),
                    ),
                  ),
                  Text(
                    txt,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF939393),
                    ),
                  ),
                  const SizedBox(height: 31),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "power_less_dialog");
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FD),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              "关闭",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "power_less_dialog");
                            onPress();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFF342A21),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              "续费会员",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFEDCAF),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                    ],
                  ),
                  const SizedBox(height: 15),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
