import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/home/<USER>';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.creation
/// @ClassName: creation_state_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/7 14:13
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/7 14:13
/// @UpdateRemark: 更新说明
class CreationStatePage extends ConsumerWidget {
  const CreationStatePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "成功提交",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Column(
        children: [
          Column(
            children: [
              Padding(padding: EdgeInsets.only(top: 68.h)),
              Image.asset(
                creationResult,
                width: 166.w,
                height: 166.h,
              ),
              Text(
                "已成功提交创作",
                style: TextStyle(
                  fontSize: 18.sp,
                  color: const Color(0xFF2D2D2D),
                ),
              ),
              SizedBox(
                height: 42.h,
              ),
              Text(
                "（生成进度及结果可前往个人中心查看）",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF747474),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 42.h,
          ),
          Row(
            children: [
              SizedBox(
                width: 25.w,
              ),
              Expanded(
                child: Container(
                  height: 47.h,
                  margin: EdgeInsets.only(bottom: 80.h),
                  child: GradientButton(
                    onPress: () {
                      context.pop();
                    },
                    radius: 12.r,
                    gradient: const LinearGradient(
                      colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                    ),
                    child: Text(
                      "回首页",
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: 13.w,
              ),
              Expanded(
                child: Container(
                  height: 47.h,
                  margin: EdgeInsets.only(bottom: 80.h),
                  child: GradientButton(
                    onPress: () {
                      context.pop();
                      ref.read(homeProvider.notifier).jumpToPage(1);
                    },
                    radius: 12.r,
                    gradient: const LinearGradient(
                      colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                    ),
                    child: Text(
                      "个人中心",
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: 25.w,
              ),
            ],
          )
        ],
      ),
    );
  }
}
