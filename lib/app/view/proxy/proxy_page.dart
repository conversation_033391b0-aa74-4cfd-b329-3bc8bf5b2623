import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../utils/prefs_util.dart';
import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: proxy_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/11 17:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/11 17:54
/// @UpdateRemark: 更新说明
class ProxyPage extends StatefulWidget {
  const ProxyPage({super.key});

  @override
  ProxyPageState createState() => ProxyPageState();
}

class ProxyPageState extends State<ProxyPage> {
  final TextEditingController _editingController = TextEditingController();
  final TextEditingController _editingPortController = TextEditingController();

  @override
  void initState() {
    _editingController.text = PrefsUtil().getString(PrefsKeys.proxyIpKey) ?? "";
    _editingPortController.text =
        PrefsUtil().getString(PrefsKeys.proxyPortKey) ?? "8888";
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("代理设置"),
        leading: const Leading(),
        elevation: 0,
      ),
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "代理设置方法：",
              style: TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            const Text(
              "1.启动代理软件",
              style: TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            const Text(
              "2.在下面输入框中填写代理端IP(一般情况是电脑IP)和端口号(默认8888，在代理软件中查看)",
              style: TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(2),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 22),
              child: TextField(
                controller: _editingController,
                keyboardType: TextInputType.number,
                maxLength: 15,
                style: const TextStyle(fontSize: 14),
                cursorColor: Colors.black,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  counterText: "",
                  hintStyle: TextStyle(
                    color: Color(0xFF999999),
                    fontSize: 14,
                  ),
                  hintText: "127.0.0.1",
                ),
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(2),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 22),
              child: TextField(
                controller: _editingPortController,
                keyboardType: TextInputType.number,
                maxLength: 15,
                style: const TextStyle(fontSize: 14),
                cursorColor: Colors.black,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  counterText: "",
                  hintStyle: TextStyle(
                    color: Color(0xFF999999),
                    fontSize: 14,
                  ),
                  hintText: "8888",
                ),
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 10)),
            const Text(
              "3.保存并重启APP",
              style: TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 16)),
            GradientButton(
              onPress: () {
                var ip = _editingController.text;
                var port = _editingPortController.text;
                PrefsUtil().setString(PrefsKeys.proxyIpKey, ip);
                PrefsUtil().setString(PrefsKeys.proxyPortKey, port);
                SmartDialog.showToast("保存成功，重启APP生效");
              },
              radius: 20,
              gradient: const LinearGradient(
                colors: [Color(0xFFFF0000), Color(0xFFFF0000)],
              ),
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: const Text(
                "保存",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
