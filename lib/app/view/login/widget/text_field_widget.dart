import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: jewelry_aigc
/// @Package: app.view.login.widget
/// @ClassName: text_field_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/2/21 17:15
/// @UpdateUser: frankylee
/// @UpdateData: 2024/2/21 17:15
/// @UpdateRemark: 更新说明
class TextFieldWidget extends ConsumerStatefulWidget {
  const TextFieldWidget({
    super.key,
    required this.hintText,
    this.isPass = false,
    this.isVerification = false,
    this.textInputType,
    this.inputFormatters,
    this.validator,
    this.onSaved,
    this.onChange,
    this.suffixWidget,
  });

  /// 输入框hint
  final String hintText;

  /// 是否是密码框
  final bool? isPass;

  /// 是否是验证码输入框
  final bool? isVerification;

  /// 输入文本类型，可以配合```inputFormatters```
  /// 实现只允许输入数字
  /// eg:
  /// textInputType: TextInputType.number,
  /// inputFormatters: <TextInputFormatter>[
  ///    FilteringTextInputFormatter.digitsOnly,
  /// ],
  final TextInputType? textInputType;

  final List<TextInputFormatter>? inputFormatters;

  /// 表单验证
  final String? Function(String?)? validator;

  /// 保存表单
  final void Function(String?)? onSaved;

  /// 输入变动
  final void Function(String)? onChange;

  /// 后缀
  final Widget? suffixWidget;

  @override
  TextFieldWidgetState createState() => TextFieldWidgetState();
}

class TextFieldWidgetState extends ConsumerState<TextFieldWidget> {
  bool obscureText = false;

  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    obscureText = widget.isPass ?? false;
  }

  /// 显示文字
  Widget suffixWidget() {
    Widget child = Container();

    /// 后缀
    if (widget.suffixWidget != null) {
      child = widget.suffixWidget!;
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        child,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onTapOutside: (event) {
        _focusNode.unfocus();
      },
      focusNode: _focusNode,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      onSaved: widget.onSaved,
      validator: widget.validator,
      onChanged: widget.onChange,
      inputFormatters: widget.inputFormatters,
      keyboardType: widget.textInputType,
      maxLength: widget.isVerification == true ? 6 : null,
      obscureText: obscureText,
      obscuringCharacter: "*",
      style: TextStyle(
        fontSize: 14.sp,
        color: Colors.black,
      ),
      cursorColor: const Color(0xFF00D873),
      maxLines: 1,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.only(top: 11.h, bottom: 1.h),
        isCollapsed: true,
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Color(0xFF00D873), width: 0.6),
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Color(0xFFEBEBEB), width: 0.6),
        ),
        errorBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.redAccent, width: 0.6),
        ),
        focusedErrorBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.redAccent, width: 0.6),
        ),
        counterText: "",
        errorText: "",
        hintStyle: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFFB3B3B3),
        ),
        hintText: widget.hintText,
        suffix: suffixWidget(),
      ),
    );
  }
}
