import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../provider/account/verification_provider.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: verification_code_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/7 11:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/7 11:45
/// @UpdateRemark: 更新说明
class VerificationCodeWidget extends StatefulWidget {
  const VerificationCodeWidget({super.key, required this.getVerCode});

  final Function()? getVerCode;

  @override
  VerificationCodeWidgetState createState() => VerificationCodeWidgetState();
}

class VerificationCodeWidgetState extends State<VerificationCodeWidget> {
  Verification? _verification;

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        _verification = ref.watch(verificationProvider.notifier);
        int ms = ref.watch(verificationProvider);
        return InkWell(
          onTap: ms == 0 ? widget.getVerCode : null,
          child: Container(
            height: 32.h,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFD8D8D8), width: 1),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Text(
              ms == 0 ? "获取验证码" : "重新发送(${ms}s)",
              style: TextStyle(
                fontSize: 14.sp,
                color:
                    ms == 0 ? const Color(0xFF000000) : const Color(0xFFC8C8C8),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _verification?.stop();
    super.dispose();
  }
}
