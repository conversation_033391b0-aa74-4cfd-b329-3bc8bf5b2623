import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/account/verification_provider.dart';
import 'package:text_generation_video/app/view/login/dialog/login_privacy_dialog.dart';
import 'package:text_generation_video/app/view/login/widget/text_field_widget.dart';
import 'package:text_generation_video/app/view/login/widget/verification_code_widget.dart';
import 'package:text_generation_video/common/ext/ext.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../provider/account/auth_provider.dart';
import '../../widgets/appbar/leading.dart';
import 'widget/agreement.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: login_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/20 15:56
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/20 15:56
/// @UpdateRemark: 更新说明
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  LoginPageState createState() => LoginPageState();
}

class LoginPageState extends ConsumerState<LoginPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((d) {
      ref.read(authProvider.notifier).logout();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        leading: const Leading(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(27.w, 16.h, 0, 52.h),
              child: Text(
                "手机号登录",
                style: TextStyle(
                  fontSize: 28.sp,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const LoginWidget(),
          ],
        ),
      ),
    );
  }
}

class LoginWidget extends ConsumerWidget {
  const LoginWidget({super.key});

  /// 登录
  void _login(BuildContext context, WidgetRef ref) async {
    if (Form.of(context).validate()) {
      var agree = ref.read(agreementAgreeProvider);
      if (!agree) {
        var result = await LoginPrivacyDialog.showPrivacy();
        if (result == false) {
          return;
        }
      }

      /// 验证通过提交数据
      if (context.mounted) {
        Form.of(context).save();
        var result = await ref.read(authProvider.notifier).login();
        if (context.mounted) {
          if (result != null) {
            context.pop();
          }
        }
      }
    }
  }

  /// 获取验证码
  void _getVerCode(BuildContext context, WidgetRef ref) async {
    var result = await ref.read(verificationProvider.notifier).getVerifyCode();
    if (result) {
      ref.read(verificationProvider.notifier).start();
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Form(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              SizedBox(
                width: 312.w,
                child: TextFieldWidget(
                  hintText: "请输入手机号码",
                  textInputType: TextInputType.number,
                  inputFormatters: <TextInputFormatter>[
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onChange: (value) {
                    ref.watch(loginPhoneProvider.notifier).setPhone(value);
                  },
                  onSaved: (value) {
                    ref.read(loginPhoneProvider.notifier).setPhone(value);
                  },
                  validator: (value) {
                    if (value != null && value.trim().isEmpty) {
                      return "手机号不能为空";
                    }
                    if (value != null && !value.isValidPhone) {
                      return "手机号码格式不正确，请重新输入";
                    }
                    return null;
                  },
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 17.h)),
              SizedBox(
                width: 312.w,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: TextFieldWidget(
                        hintText: "请输入验证码",
                        isVerification: true,
                        textInputType: TextInputType.number,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        onSaved: (value) {
                          ref
                              .read(loginVerCodeProvider.notifier)
                              .setVerCode(value);
                        },
                        validator: (value) {
                          if (value != null && value.trim().isEmpty) {
                            return "验证码不能为空";
                          }
                          return null;
                        },
                      ),
                    ),
                    Padding(padding: EdgeInsets.only(right: 16.w)),
                    Consumer(builder: (context, ref, child) {
                      return VerificationCodeWidget(
                        getVerCode: () => _getVerCode(context, ref),
                      );
                    }),
                  ],
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 55.h)),
              Consumer(
                builder: (context, ref, child) {
                  return SizedBox(
                    width: 299.w,
                    height: 49.h,
                    child: GradientButton(
                      radius: 10.r,
                      onPress: () => _login(context, ref),
                      gradient: const LinearGradient(
                        colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                      ),
                      child: Text(
                        "登录",
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  );
                },
              ),
              const LoginAgreement(),
            ],
          ),
        ],
      ),
    );
  }
}

class LoginAgreement extends ConsumerWidget {
  const LoginAgreement({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.only(top: 30.h),
      child: Agreement(
        initValue: ref.watch(agreementAgreeProvider),
        onChange: (value) {
          ref.read(agreementAgreeProvider.notifier).setAgree(value);
        },
      ),
    );
  }
}
