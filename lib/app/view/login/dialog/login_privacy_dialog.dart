import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../navigation/router.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting.dialog
/// @ClassName: login_privacy_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/11 11:44
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/11 11:44
/// @UpdateRemark: 更新说明
class LoginPrivacyDialog {
  static Future<bool?> showPrivacy() async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "login_privacy_dialog",
      builder: (context) {
        List<String> content = [
          "请充分阅读并理解",
          "《用户协议》",
          "与",
          "《隐私政策》",
          "，点击同意按钮代表您已知悉并同意签署协议",
        ];
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 22.h, bottom: 12.h),
                    child: Text(
                      "服务协议与隐私保护",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 25.w),
                    child: HighlightText(
                      data: content,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                      ),
                      keyStyle: TextStyle(
                        color: const Color(0xFF000000),
                        fontSize: 14.sp,
                      ),
                      keys: const [
                        "《隐私政策》",
                        "《用户协议》",
                      ],
                      onTapCallback: (String key) {
                        /// keys点击
                        if (key == "《隐私政策》") {
                          navigatorKey.currentContext
                              ?.push("/$privacyPolicyPage");
                        } else if (key == "《用户协议》") {
                          navigatorKey.currentContext
                              ?.push("/$userAgreementPage");
                        }
                      },
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 11.h)),
                  Divider(
                    indent: 5.w,
                    endIndent: 5.w,
                    color: const Color(0xFFE6E6E6),
                    height: 1,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "login_privacy_dialog",
                              result: false,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "取消",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 24.h,
                        color: const Color(0xFFE6E6E6),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "login_privacy_dialog",
                              result: true,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "同意并继续",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF1356FF),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
