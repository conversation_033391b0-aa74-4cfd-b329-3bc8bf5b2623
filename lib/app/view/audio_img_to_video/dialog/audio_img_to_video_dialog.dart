import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/app/provider/audio_img_to_video/audio_case_provider.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:video_player/video_player.dart';
import '../../../repository/modals/audio_img_to_video/audio_img_to_video.dart';

/// 对口型案例视频播放对话框
class AudioImgToVideoDialog {
  static void showAudioImgToVideoDialog(BuildContext context, AudioImgToVideo audioImgToVideo) {
    SmartDialog.show(
      builder: (context) {
        return AudioImgToVideoDialogWidget(audioImgToVideo: audioImgToVideo);
      },
    );
  }
}

class AudioImgToVideoDialogWidget extends ConsumerStatefulWidget {
  const AudioImgToVideoDialogWidget({super.key, required this.audioImgToVideo});

  final AudioImgToVideo audioImgToVideo;

  @override
  ConsumerState<AudioImgToVideoDialogWidget> createState() =>
      _AudioImgToVideoDialogWidgetState();
}

class _AudioImgToVideoDialogWidgetState
    extends ConsumerState<AudioImgToVideoDialogWidget> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  void _initializeVideoPlayer() {
    if (widget.audioImgToVideo.videoUrl.isNotEmpty) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.audioImgToVideo.videoUrl),
      )..initialize().then((_) {
          setState(() {
            _isInitialized = true;
            _controller.play();
            _controller.setLooping(true);
          });
        }).catchError((error) {
          debugPrint('视频初始化错误: $error');
        });
    }
  }

  @override
  void dispose() {
    if (widget.audioImgToVideo.videoUrl.isNotEmpty) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 269,
      height: 361,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child:
                  widget.audioImgToVideo.videoUrl.isNotEmpty && _isInitialized
                      ? AspectRatio(
                          aspectRatio: _controller.value.aspectRatio,
                          child: VideoPlayer(_controller),
                        )
                      : Container(
                          color: Colors.black,
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
            ),
          ),
          Positioned(
            top: 10,
            right: 10,
            child: InkWell(
              onTap: () {
                SmartDialog.dismiss();
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 18),
              ),
            ),
          ),
          Positioned(
            bottom: 11,
            left: 0,
            right: 0,
            child: GradientButton(
              onPress: () {
                // 选中当前案例并关闭弹窗
                ref
                    .read(audioCaseToVideoCaseProvider.notifier)
                    .selectCase(widget.audioImgToVideo);
                SmartDialog.dismiss();
              },
              padding: const EdgeInsets.symmetric(vertical: 14),
              margin: const EdgeInsets.symmetric(horizontal: 10),
              radius: 23,
              shadow: false,
              gradient: const LinearGradient(
                colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
              ),
              child: const Text(
                "创作同款",
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF18161A),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
