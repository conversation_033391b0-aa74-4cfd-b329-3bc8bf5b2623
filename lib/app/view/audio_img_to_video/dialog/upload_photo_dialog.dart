import 'package:flutter/material.dart';
import 'package:text_generation_video/app/provider/audio_img_to_video/audio_case_provider.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/upload_img_adivce.dart';
import 'package:text_generation_video/config/icon_address.dart';

/// 上传形象建议弹窗
class UploadPhotoDialogWidget extends StatelessWidget {
  final PageType pageType;
  final VoidCallback onTakePhoto;
  final VoidCallback onSelectPhoto;
  final UploadImgAdivceData petSingUploadImgAdivceData;

  UploadPhotoDialogWidget({
    super.key,
    required this.pageType,
    required this.onTakePhoto,
    required this.onSelectPhoto,
  }) : petSingUploadImgAdivceData = _getUploadImgAdivceData(pageType);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            "上传形象建议",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 26),
          _buildImageExample(),
          const SizedBox(height: 28),
          _buildPhotoOptions(),
          const SizedBox(height: 16),
          _buildDisclaimerText(),
          const SizedBox(height: 16),
          _buildButtons(context, onTakePhoto, onSelectPhoto),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildImageExample() {
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Transform.rotate(
            angle: 10 * 3.14159 / -180, // 将角度转换为弧度
            child: Container(
              height: 200,
              width: 200,
              decoration: BoxDecoration(
                color: const Color(0xff3A383D),
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),
        Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color(0xFF222123),
              width: 3,
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 这里可以放置示例图片
              Positioned(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Stack(
                    children: [
                      Image.network(
                        petSingUploadImgAdivceData.correctExamples.imageUrl ?? "",
                        fit: BoxFit.cover,
                      ),
                      // 添加底部渐变阴影
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: 80,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withAlpha(100),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                bottom: 12,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        correctExamIcon,
                        width: 18,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        petSingUploadImgAdivceData.correctExamples.title ?? "",
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: 12,
                left: 12,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: .4),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Text(
                    "形象示例",
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPhotoOptions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: petSingUploadImgAdivceData.errorExamples
          .map((item) => Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: _buildPhotoOption(item.title!, item.imageUrl!),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildPhotoOption(String text, String imageUrl) {
    return Column(
      children: [
        AspectRatio(
          aspectRatio: 1,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              errorExamIcon,
              width: 18,
            ),
            const SizedBox(width: 6),
            Text(
              text,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white,
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _buildDisclaimerText() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: const Text(
        "本功能将可能采取敏感信息用于AI合成，如上传非本人信息、请确保已获得权利人的明确许可且不会用于违法违规及侵权行为。",
        style: TextStyle(
          fontSize: 12,
          color: Colors.white54,
        ),
      ),
    );
  }

  Widget _buildButtons(
    BuildContext context,
    VoidCallback? onTakePhoto,
    VoidCallback? onSelectPhoto,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 140,
          child: _buildButton(
            "直接拍摄",
            const Color(0xff234C44),
            () {
              Navigator.pop(context);
              onTakePhoto?.call();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildButton(
            "相册倒入",
            const Color(0xFF30E6B8),
            () {
              Navigator.pop(context);
              onSelectPhoto?.call();
            },
            isMain: true,
          ),
        ),
      ],
    );
  }

  Widget _buildButton(String text, Color color, VoidCallback onTap,
      {bool isMain = false}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 140,
        height: 56,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(16),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: isMain ? Colors.black : const Color(0xff30E6B8),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  static UploadImgAdivceData _getUploadImgAdivceData(PageType pageType) {
    switch (pageType) {
      case PageType.petSinging:
        return UploadImgAdivceData(
          correctExamples: UploadImgAdivce(
            title: '宠物正面大头照/半身照',
            imageUrl:
                'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
          ),
          errorExamples: [
            UploadImgAdivce(
              title: '侧面',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '挡住嘴巴',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '面部遮挡',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '漫画风格',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
          ],
        );
      case PageType.syncSinging:
        return UploadImgAdivceData(
          correctExamples: UploadImgAdivce(
            title: '单头正面大头照/半身照',
            imageUrl:
                'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
          ),
          errorExamples: [
            UploadImgAdivce(
              title: '头部倾斜',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '露出手部',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '面部遮挡',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '漫画风格',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
          ],
        );
      default:
        return UploadImgAdivceData(
          correctExamples: UploadImgAdivce(
            title: '默认正面大头照/半身照',
            imageUrl:
                'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
          ),
          errorExamples: [
            UploadImgAdivce(
              title: '默认侧面',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '默认挡住嘴巴',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '默认面部遮挡',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
            UploadImgAdivce(
              title: '默认漫画风格',
              imageUrl:
                  'https://cdn.camera.msmds.cn/59/2025-09-08/68be7ba2e4b0da45f1fa984e.jpg',
            ),
          ],
        );
    }
  }
  
}