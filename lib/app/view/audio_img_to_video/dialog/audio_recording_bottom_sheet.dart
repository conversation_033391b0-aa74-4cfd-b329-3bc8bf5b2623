import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:text_generation_video/config/icon_address.dart';

/// 录制完成回调
typedef OnRecordingComplete = void Function(
    String audioFilePath, Duration duration);

/// 音频录制底部弹窗
class AudioRecordingBottomSheet extends StatefulWidget {
  const AudioRecordingBottomSheet({
    super.key,
    this.maxDuration = const Duration(seconds: 15),
    this.onRecordingComplete,
    this.onCancel,
  });

  final Duration maxDuration;
  final OnRecordingComplete? onRecordingComplete;
  final VoidCallback? onCancel;

  @override
  State<AudioRecordingBottomSheet> createState() =>
      _AudioRecordingBottomSheetState();
}

class _AudioRecordingBottomSheetState extends State<AudioRecordingBottomSheet>
    with TickerProviderStateMixin {
  final AudioRecorder _audioRecorder = AudioRecorder();

  bool _isRecording = false;
  bool _isPermissionGranted = false;
  bool _isInitializing = true;
  Duration _currentDuration = Duration.zero;
  Timer? _timer;
  String? _audioFilePath;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _initializeAnimations();
    _checkPermissionAndInitialize();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _checkPermissionAndInitialize() async {
    final permission = await _audioRecorder.hasPermission();
    setState(() {
      _isPermissionGranted = permission;
      _isInitializing = false;
    });
  }

  Future<void> _startRecording() async {
    if (!_isPermissionGranted) return;

    try {
      // 获取临时目录用于录制
      final tempDir = await getTemporaryDirectory();
      final fileName = 'audio_${DateTime.now().millisecondsSinceEpoch}.m4a';
      _audioFilePath = '${tempDir.path}/$fileName';

      // Start recording
      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _audioFilePath!,
      );

      setState(() {
        _isRecording = true;
        _currentDuration = Duration.zero;
      });

      // Start animations
      _pulseController.repeat(reverse: true);

      // Start timer
      _timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
        setState(() {
          _currentDuration = Duration(milliseconds: timer.tick * 100);
        });

        // Auto-stop when max duration is reached
        if (_currentDuration >= widget.maxDuration) {
          _stopRecording();
        }
      });
    } catch (e) {
      debugPrint('Error starting recording: $e');
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;

    try {
      await _audioRecorder.stop();
      _timer?.cancel();
      _pulseController.stop();

      setState(() {
        _isRecording = false;
      });

      if (_audioFilePath != null && widget.onRecordingComplete != null) {
        widget.onRecordingComplete!(_audioFilePath!, _currentDuration);
      }
    } catch (e) {
      debugPrint('Error stopping recording: $e');
    }
  }

  void _cancelRecording() async {
    if (_isRecording) {
      await _audioRecorder.stop();
      _timer?.cancel();
      _pulseController.stop();

      // 删除录制的音频文件
      if (_audioFilePath != null) {
        final file = File(_audioFilePath!);
        if (await file.exists()) {
          await file.delete();
        }
      }
    }

    widget.onCancel?.call();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          // Container(
          //   margin: const EdgeInsets.only(top: 12),
          //   width: 40,
          //   height: 4,
          //   decoration: BoxDecoration(
          //     color: const Color(0xFF565656),
          //     borderRadius: BorderRadius.circular(2),
          //   ),
          // ),

          Padding(
            padding: const EdgeInsets.fromLTRB(24, 8, 24, 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                const SizedBox(height: 32),
                if (_isInitializing) _buildInitializing(),
                if (!_isInitializing && !_isPermissionGranted)
                  _buildPermissionDenied(),
                if (!_isInitializing && _isPermissionGranted)
                  _buildRecordingInterface(),
                const SizedBox(height: 32),
                _buildControls(),
                SizedBox(height: MediaQuery.of(context).padding.bottom),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Row(
          children: [
            const Text(
              "录制音频",
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 2),
            Image.asset(
              recoedAudioIcon,
              width: 30,
            )
          ],
        ),
        const Spacer(),
        IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: const Color(0xFF3E3C3E),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF858585),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 18,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInitializing() {
    return const Column(
      children: [
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF30E6B8)),
        ),
        SizedBox(height: 16),
        Text(
          "正在初始化...",
          style: TextStyle(
            color: Color(0xFF8A8D93),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildPermissionDenied() {
    return const Column(
      children: [
        Icon(
          Icons.mic_off,
          color: Color(0xFFFF4757),
          size: 64,
        ),
        SizedBox(height: 16),
        Text(
          "需要麦克风权限才能录制音频",
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        SizedBox(height: 8),
        Text(
          "请在设置中允许应用访问麦克风",
          style: TextStyle(
            color: Color(0xFF8A8D93),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildRecordingInterface() {
    final remainingSeconds =
        (widget.maxDuration.inSeconds - _currentDuration.inSeconds)
            .clamp(0, widget.maxDuration.inSeconds);

    return Column(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _isRecording ? _pulseAnimation.value : 1.0,
              child: Container(
                width: 140,
                height: 140,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Colors.transparent,
                      Color(0x5530E6B8),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  shape: BoxShape.circle,
                  // 渐变边框
                  border: Border.all(
                    color: const Color(0x6630E6B8),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    "$remainingSeconds",
                    style: const TextStyle(
                      color: Color(0xFF30E6B8),
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildControls() {
    if (_isInitializing || !_isPermissionGranted) {
      return SizedBox(
        width: double.infinity,
        child: TextButton(
          onPressed: _cancelRecording,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text(
            "取消",
            style: TextStyle(
              color: Color(0xFF8A8D93),
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isRecording ? _stopRecording : _startRecording,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF30E6B8),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: Text(
              _isRecording ? "结束录制" : "开始录制",
              style: const TextStyle(
                color: Color(0xFF18161A),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
