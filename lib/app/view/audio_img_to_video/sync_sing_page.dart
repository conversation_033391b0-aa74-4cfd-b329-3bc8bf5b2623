import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/audio_img_to_video/audio_case_provider.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/widget/audio_case_img_to_video.dart';

import '../../widgets/appbar/leading.dart';

class SyncSingPage extends ConsumerWidget {
  const SyncSingPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "对口型唱歌",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(color: Colors.white,),
      ),
      body: const AudioCaseImgToVideoWidget(
        pageType: PageType.syncSinging,
      ),
    );
  }
}