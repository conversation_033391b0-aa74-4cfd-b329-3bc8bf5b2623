import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/agent/agent_chat_provider.dart';
import 'package:ui_widgets/ui_widgets.dart' show LoadState;

import '../../../../config/icon_address.dart';
import '../../../../utils/platform_util.dart';
import '../../../provider/in_app_purchase/app_purchase_provider.dart';
import '../../home/<USER>/pay_info_dialog.dart';

class AgentHeaderWidget extends ConsumerStatefulWidget {
  const AgentHeaderWidget({
    super.key,
    required this.conversationId,
    required this.botId,
  });

  final String? conversationId;
  final String? botId;

  @override
  AgentHeaderWidgetState createState() => AgentHeaderWidgetState();
}

class AgentHeaderWidgetState extends ConsumerState<AgentHeaderWidget> {
  void _buyPackage(BuildContext context, WidgetRef ref) async {
    if (PlatformUtils.isIOS) {
      var list = await ref.read(appPurchaseProvider.notifier).loadAllProducts();
      if (list != null && list.isNotEmpty && context.mounted) {
        ApplePayDialog.showApplePayDialog(
          context,
          ref,
          list,
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    debugPrint("AgentHeaderWidgetState--------");
    WidgetsBinding.instance.addPostFrameCallback((d) {
      ref.read(openingRemarksProvider.notifier).getBotInfo(widget.botId);
    });
  }

  @override
  Widget build(BuildContext context) {
    var botData = ref.watch(openingRemarksProvider);
    var footerState = ref.watch(currentMsgListFooterStateProvider);
    if (footerState != LoadState.noMore) return const SizedBox();
    if (botData == null) return const SizedBox();
    var list = botData.bot?.onboarding_info?.suggested_questions;
    List<Widget> desList = [];
    if (list != null && list.isNotEmpty) {
      desList = list
          .map(
            (e) => InkWell(
              onTap: () {
                ref.read(agentChatMessageProvider.notifier).sendMessage(
                  widget.conversationId,
                  e,
                  () {
                    _buyPackage(context, ref);
                  },
                );
              },
              child: Container(
                margin: const EdgeInsets.only(bottom: 10),
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 10,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFEBFFEB),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        e,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
          .toList();
    }
    return Container(
      padding: const EdgeInsets.only(top: 20),
      child: Column(
        children: [
          Image.asset(chatAvatar, width: 105, height: 96),
          const SizedBox(height: 15),
          Text(
            "${botData.bot?.name}",
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 15),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            padding: const EdgeInsets.fromLTRB(10, 13, 10, 15),
            decoration: BoxDecoration(
              color: const Color(0xFFF4FFFE),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "${botData.bot?.onboarding_info?.prologue}",
                        style: const TextStyle(
                          fontSize: 18,
                          color: Color(0xFF00D371),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                ...desList,
              ],
            ),
          ),
        ],
      ),
    );
  }
}
