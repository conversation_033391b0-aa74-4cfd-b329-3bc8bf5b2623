import 'package:flutter/material.dart';

class BubblePainter extends CustomPainter {
  final bool isMe;

  BubblePainter({required this.isMe});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = isMe ? const Color(0xFF00D371) : const Color(0xFFF8F8F8)
      ..style = PaintingStyle.fill;

    final path = Path();
    const radius = 10.0;

    if (isMe) {
      // 右边尖角气泡
      path.moveTo(0, radius);
      path.quadraticBezierTo(0, 0, radius, 0);
      path.lineTo(size.width - radius - 5, 0);
      path.quadraticBezierTo(size.width - 5, 0, size.width - 5, radius);
      path.lineTo(size.width - 5, size.height - radius);
      path.quadraticBezierTo(
          size.width - 5, size.height, size.width - radius - 5, size.height);
      path.lineTo(radius, size.height);
      path.quadraticBezierTo(0, size.height, 0, size.height - radius);
      path.close();

      // 尖角
      path.moveTo(size.width - 5, 14);
      path.lineTo(size.width, 18);
      path.lineTo(size.width - 5, 22);
      path.close();
    } else {
      // 左边尖角气泡
      path.moveTo(5, radius);
      path.quadraticBezierTo(5, 0, 5 + radius, 0);
      path.lineTo(size.width - radius, 0);
      path.quadraticBezierTo(size.width, 0, size.width, radius);
      path.lineTo(size.width, size.height - radius);
      path.quadraticBezierTo(size.width, size.height, size.width - radius, size.height);
      path.lineTo(5 + radius, size.height);
      path.quadraticBezierTo(5, size.height, 5, size.height - radius);
      path.close();

      // 尖角
      path.moveTo(5, 14);
      path.lineTo(0, 18);
      path.lineTo(5, 22);
      path.close();
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant BubblePainter oldDelegate) {
    return oldDelegate.isMe != isMe;
  }
}