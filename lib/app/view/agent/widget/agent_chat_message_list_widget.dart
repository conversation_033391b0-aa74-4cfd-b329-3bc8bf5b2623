import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/view/agent/widget/agent_header_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../provider/agent/agent_chat_provider.dart';
import 'agent_message_widget.dart';
import 'agent_reply_loading_widget.dart';
import 'agent_user_message_widget.dart';

class AgentChatMessageListWidget extends ConsumerStatefulWidget {
  const AgentChatMessageListWidget({
    super.key,
    required this.id,
    required this.botId,
  });

  final String? id;
  final String? botId;

  @override
  AgentChatMessageListWidgetState createState() =>
      AgentChatMessageListWidgetState();
}

class AgentChatMessageListWidgetState
    extends ConsumerState<AgentChatMessageListWidget> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((d) {
      ref
          .read(agentChatHistoryMessageProvider.notifier)
          .loadHistoryMessage(widget.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return MessageCustomListView(
      reverse: true,
      footer: AgentHeaderWidget(conversationId: widget.id, botId: widget.botId),
      header: const AgentReplyLoadingWidget(),
      onLoadMore: () async {
        ref
            .read(agentChatHistoryMessageProvider.notifier)
            .loadMoreHistoryMessage(widget.id);
      },
      footerState: ref.watch(currentMsgListFooterStateProvider),
      padding: const EdgeInsets.symmetric(vertical: 20),
      historyData: ref.watch(agentChatHistoryMessageProvider),
      data: ref.watch(agentChatMessageProvider),
      renderItem: (context, index, o) {
        o as MessageItem;
        if (o.attribution == 0) {
          return AgentUserMessageWidget(messageItem: o);
        }
        return AgentMessageWidget(
          messageItem: o,
          needAnima: index == 0 && o.conversationMsgItem == null,
        );
      },
    );
  }
}
