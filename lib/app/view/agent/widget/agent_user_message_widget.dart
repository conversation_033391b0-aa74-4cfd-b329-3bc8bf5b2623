import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/agent/agent_chat_provider.dart';
import 'package:text_generation_video/app/view/agent/widget/bubble_painter.dart';

import '../../../../config/icon_address.dart';

class AgentUserMessageWidget extends ConsumerWidget {
  const AgentUserMessageWidget({
    super.key,
    required this.messageItem,
  });

  final MessageItem messageItem;

  Widget _contentWidget() {
    // 文本消息
    if (messageItem.type == "text") {
      return SelectableText(
        "${messageItem.conversationMsgItem?.content ?? messageItem.message}",
        style: const TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
      );
    }
    if (messageItem.type == "object_string") {
      var rawData = messageItem.conversationMsgItem?.content ?? "";
      final List<dynamic> data = jsonDecode(rawData);
      String? showText;
      // String? fileUrl;
      String? fileName;
      for (var item in data) {
        if (item['type'] == 'text') {
          showText = item['text'];
        }
        if (item['type'] == 'file') {
          // fileUrl = item['file_url'];
          fileName = item['name'];
        }
      }
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  uploadFileIcon,
                  width: 21,
                  height: 26,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text("$fileName"),
                ),
              ],
            ),
          ),
          if (showText != null && showText.isNotEmpty)
            Text(
              showText,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),
        ],
      );
    }
    return const Text(
      "暂未支持的消息",
      style: TextStyle(
        fontSize: 16,
        color: Colors.white,
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 60),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                CustomPaint(
                  painter: BubblePainter(isMe: true),
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(10, 10, 15, 10),
                    child: _contentWidget(),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Image.asset(
            avatar,
            width: 40,
            height: 40,
          ),
          const SizedBox(width: 10),
        ],
      ),
    );
  }
}
