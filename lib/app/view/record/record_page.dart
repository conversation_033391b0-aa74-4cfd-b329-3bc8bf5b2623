import 'package:extended_image/extended_image.dart' as ext_img;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/agent/works_record_provider.dart';
import 'package:text_generation_video/app/provider/creation/creation_provider.dart';
import 'package:text_generation_video/app/repository/modals/agent/work_detail.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../widgets/appbar/leading.dart';

class RecordPage extends ConsumerStatefulWidget {
  const RecordPage({
    super.key,
    required this.data,
  });

  final Map data;

  @override
  RecordPageState createState() => RecordPageState();
}

class RecordPageState extends ConsumerState<RecordPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((d) {
      ref
          .read(worksListRecordProvider.notifier)
          .loadData(widget.data["conversationId"]);
      // .loadData("7490470923380932623");
    });
  }

  // item
  Widget _buildRecordItem(
    BuildContext context,
    WidgetRef ref,
    WorkDetailList? workDetailList,
  ) {
    if (workDetailList == null || workDetailList.detailList == null) {
      return const SizedBox();
    }
    var workType = workDetailList.detailList?.first.workType;
    Widget child = ListView.builder(
      padding: const EdgeInsets.only(left: 4),
      scrollDirection: Axis.horizontal,
      itemBuilder: (_, int index) {
        var item = workDetailList.detailList?[index];
        return _buildChildItem(
          context,
          ref,
          item,
          workDetailList.detailList,
          index,
        );
      },
      itemCount: workDetailList.detailList?.length,
    );
    return Container(
      padding: const EdgeInsets.only(left: 13, right: 3),
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 2,
                height: 14,
                color: const Color(0xFF00D773),
              ),
              const SizedBox(width: 2),
              Text(
                workDetailList.workTitle ?? "",
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 1),
          Padding(
            padding: const EdgeInsets.only(left: 6),
            child: Text(
              "${workDetailList.createTime}",
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black,
              ),
            ),
          ),
          const SizedBox(height: 6),
          SizedBox(
            height: workType != 1 ? 130 : 56,
            child: child,
          ),
        ],
      ),
    );
  }

  Widget _buildChildItem(
    BuildContext context,
    WidgetRef ref,
    WorkDetail? workDetail,
    List<WorkDetail?>? detailsList,
    int index,
  ) {
    var type = workDetail?.workType;
    if (type == 1) {
      // 文件
      return const SizedBox();
      // return Container(
      //   width: 189,
      //   height: 56,
      //   margin: const EdgeInsets.only(right: 10),
      //   decoration: BoxDecoration(
      //     color: const Color(0xFFF8F8F8),
      //     borderRadius: BorderRadius.circular(6),
      //   ),
      //   child: Stack(
      //     alignment: Alignment.center,
      //     children: [
      //       Row(
      //         mainAxisAlignment: MainAxisAlignment.center,
      //         children: [
      //           Image.asset(uploadFileIcon, width: 21, height: 26),
      //           const SizedBox(width: 10),
      //           const Text(
      //             "文件名称XXX.pdf",
      //             style: TextStyle(
      //               fontSize: 14,
      //               color: Colors.black,
      //             ),
      //           ),
      //         ],
      //       ),
      //       Positioned(
      //         bottom: 4,
      //         right: 4,
      //         child: InkWell(
      //           onTap: () {
      //             ref
      //                 .read(creationVideoProvider.notifier)
      //                 .downloadFile("url", "test.pdf");
      //           },
      //           child: Container(
      //             padding:
      //                 const EdgeInsets.symmetric(vertical: 2, horizontal: 7),
      //             decoration: BoxDecoration(
      //               color: const Color(0x50000000),
      //               borderRadius: BorderRadius.circular(3),
      //             ),
      //             child: Image.asset(
      //               recordDownIcon,
      //               width: 9,
      //               height: 10,
      //             ),
      //           ),
      //         ),
      //       ),
      //     ],
      //   ),
      // );
    } else if (type == 2) {
      //图片
      return InkWell(
        onTap: () {
          context.push(
            '/$recordShowerPage',
            extra: {
              "initPage": index,
              "list": detailsList,
            },
          );
        },
        child: Container(
          width: 130,
          height: 130,
          margin: const EdgeInsets.only(right: 10),
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F8F8),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              ext_img.ExtendedImage.network(
                "${workDetail?.workConent}",
                fit: BoxFit.contain,
                enableSlideOutPage: true,
                mode: ext_img.ExtendedImageMode.none,
                printError: false,
                loadStateChanged: (state) {
                  if (state.extendedImageLoadState == ext_img.LoadState.failed) {
                    return Container(
                      width: 130,
                      height: 130,
                      color: Colors.grey,
                    );
                  }
                  return null;
                },
              ),
              Positioned(
                bottom: 9,
                right: 9,
                child: InkWell(
                  onTap: () {
                    ref
                        .read(creationVideoProvider.notifier)
                        .downloadPhoto(workDetail?.workConent);
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 2, horizontal: 7),
                    decoration: BoxDecoration(
                      color: const Color(0x50000000),
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Image.asset(
                      recordDownIcon,
                      width: 9,
                      height: 10,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (type == 3) {
      // 视频
      return const SizedBox();
      // return Container(
      //   width: 130,
      //   height: 130,
      //   margin: const EdgeInsets.only(right: 10),
      //   padding: const EdgeInsets.all(5),
      //   decoration: BoxDecoration(
      //     color: const Color(0xFFF8F8F8),
      //     borderRadius: BorderRadius.circular(6),
      //   ),
      //   child: Stack(
      //     alignment: Alignment.center,
      //     children: [
      //       Image.asset(descBg),
      //       Container(
      //         width: 23,
      //         height: 23,
      //         decoration: BoxDecoration(
      //           border: Border.all(color: Colors.white, width: 2),
      //           borderRadius: BorderRadius.circular(15),
      //         ),
      //         child: const Icon(
      //           Icons.play_arrow_rounded,
      //           size: 16,
      //           color: Colors.white,
      //         ),
      //       ),
      //       Positioned(
      //         bottom: 4,
      //         right: 4,
      //         child: InkWell(
      //           onTap: () {},
      //           child: Container(
      //             padding:
      //                 const EdgeInsets.symmetric(vertical: 2, horizontal: 7),
      //             decoration: BoxDecoration(
      //               color: const Color(0x50000000),
      //               borderRadius: BorderRadius.circular(3),
      //             ),
      //             child: Image.asset(
      //               recordDownIcon,
      //               width: 9,
      //               height: 10,
      //             ),
      //           ),
      //         ),
      //       ),
      //     ],
      //   ),
      // );
    } else {
      return const SizedBox();
    }
  }

  @override
  Widget build(BuildContext context) {
    var list = ref.watch(
      worksListRecordProvider.select((value) => value.works),
    );
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "作品记录",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: CustomListView(
        padding: const EdgeInsets.only(top: 10),
        data: list,
        onLoadMore: () async {
          ref
              .read(worksListRecordProvider.notifier)
              .loadMore(widget.data["conversationId"]);
          // .loadMore("7490470923380932623");
        },
        footerState: ref.watch(
          worksListRecordProvider.select((value) => value.loadState),
        ),
        renderItem: (context, index, o) {
          var item = list?[index];
          return _buildRecordItem(context, ref, item);
        },
        empty: Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.only(
            top: 70,
          ),
          child: const Text(
            "暂无数据",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ),
      ),
    );
  }
}
