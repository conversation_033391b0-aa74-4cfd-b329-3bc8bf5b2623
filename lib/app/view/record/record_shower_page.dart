import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../repository/modals/agent/work_detail.dart';

class RecordShowerPage extends ConsumerWidget {
  const RecordShowerPage({
    super.key,
    required this.mapData,
  });

  final Map mapData;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var data = mapData["list"] as List<WorkDetail?>?;
    if (data == null) return const SizedBox();
    return ExtendedImageSlidePage(
      slideAxis: SlideAxis.vertical,
      slidePageBackgroundHandler: (offset, size) {
        double opacity = 1.0 - (offset.dy.abs() / 500).clamp(0.0, 1.0);
        return Colors.black.withValues(alpha: opacity);
      },
      child: ExtendedImageGesturePageView.builder(
        controller: ExtendedPageController(
          initialPage: mapData["initPage"] ?? 0,
        ),
        scrollDirection: Axis.horizontal,
        itemCount: data.length,
        itemBuilder: (context, index) {
          Widget image = InkWell(
            onTap: () {
              context.pop();
            },
            child: ExtendedImage.network(
              data[index]?.workConent ?? "",
              fit: BoxFit.contain,
              enableSlideOutPage: true,
              mode: ExtendedImageMode.none,
              printError: false,
              loadStateChanged: (state) {
                if (state.extendedImageLoadState == LoadState.failed) {
                  return Container(
                    color: Colors.grey,
                  );
                }
                return null;
              },
            ),
          );
          return image;
        },
      ),
    );
  }
}
