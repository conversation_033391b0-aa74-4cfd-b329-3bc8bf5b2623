import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/creation/creation_provider.dart';
import 'package:text_generation_video/app/repository/modals/voice/creation_result.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:video_player/video_player.dart';

import '../../../utils/date_util.dart';
import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: text_generation_video
/// @Package: app.view.preview
/// @ClassName: preview_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/7 17:48
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/7 17:48
/// @UpdateRemark: 更新说明
class PreviewPage extends StatefulWidget {
  const PreviewPage({
    super.key,
    required this.creationResult,
  });

  final CreationResult creationResult;

  @override
  State<StatefulWidget> createState() => PreviewPageState();
}

class PreviewPageState extends State<PreviewPage> {
  late VideoPlayerController _controller;

  bool showPlay = false;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    debugPrint("creationResult: ${widget.creationResult.videoUrl}");
    var videoUrl = widget.creationResult.videoUrl ??
        'https://alicdn.msmds.cn/text_to_video_demo.mp4';
    var uri = Uri.parse(videoUrl);
    _controller = VideoPlayerController.networkUrl(uri)
      ..initialize().then(
        (_) {
          // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
          debugPrint("initialize success------");
          setState(() {});
        },
      ).catchError(
        (e) {
          debugPrint('catchError: $e');
        },
      );
    _controller.addListener(handle);
  }

  @override
  void dispose() {
    _controller.removeListener(handle);
    _controller.dispose();
    super.dispose();
  }

  void handle() {
    setState(() {});
  }

  void startTimer() {
    int ms = 2;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (ms == 0) {
        timer.cancel();
        if (_controller.value.isPlaying) {
          setState(() {
            showPlay = false;
          });
        }
      } else {
        ms--;
      }
    });
  }

  Widget _buildVideo() {
    Widget child;
    debugPrint("isInitialized: ${_controller.value.isInitialized}");
    if (_controller.value.isInitialized) {
      debugPrint("isInitialized: ${_controller.value.aspectRatio}");
      var aspectRatio = _controller.value.aspectRatio;
      double width;
      double height;
      if (aspectRatio > 1) {
        /// 宽高比大于1
        width = 335.w;
        height = width / aspectRatio;
      } else {
        height = 251.h;
        width = height * aspectRatio;
      }
      Widget pauseWidget = Container();
      if (!_controller.value.isPlaying) {
        pauseWidget = InkWell(
          onTap: () {
            setState(() {
              _controller.play();
              showPlay = false;
            });
          },
          child: Image.asset(
            pauseIcon,
            width: 60.w,
            height: 60.h,
            fit: BoxFit.contain,
          ),
        );
      }
      if (_controller.value.isPlaying && showPlay) {
        pauseWidget = InkWell(
          onTap: () {
            setState(() {
              _controller.pause();
            });
          },
          child: Image.asset(
            playIcon,
            width: 60.w,
            height: 60.h,
            fit: BoxFit.contain,
          ),
        );
      }
      child = Stack(
        alignment: Alignment.center,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                showPlay = true;
                startTimer();
              });
            },
            child: SizedBox(
              width: width,
              height: height,
              child: VideoPlayer(_controller),
            ),
          ),
          pauseWidget,
          Positioned(
            bottom: 6.h,
            child: Row(
              children: [
                Text(
                  DateUtil.durationToMinSec(_controller.value.position),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                  ),
                ),
                Container(
                  width: 190.w,
                  margin: EdgeInsets.symmetric(horizontal: 5.w),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTickMarkColor: Colors.white,
                      activeTrackColor: const Color(0xFF1C63F2),
                      inactiveTrackColor: const Color(0xFFDADCDE),
                      trackHeight: 2.h,
                      thumbShape: CircleSliderThumb(thumbRadius: 7.h),
                      overlayShape:
                          const RoundSliderOverlayShape(overlayRadius: 0.0),
                    ),
                    child: Slider(
                      activeColor: Colors.white,
                      inactiveColor: Colors.white.withAlpha(60),
                      min: 0,
                      max: DateUtil.durationToSeconds(
                        _controller.value.duration,
                      ),
                      value: DateUtil.durationToSeconds(
                        _controller.value.position,
                      ),
                      onChanged: (value) {
                        _controller.seekTo(DateUtil.secondsToDuration(value));
                      },
                    ),
                  ),
                ),
                Text(
                  DateUtil.durationToMinSec(_controller.value.duration),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    } else {
      child = const Center(
        child: CircularProgressIndicator(
          color: Colors.green,
          strokeWidth: 1,
        ),
      );
    }
    return Container(
      width: 335.w,
      height: 251.h,
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "预览",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(padding: EdgeInsets.only(top: 12.h)),
              _buildVideo(),
              Padding(padding: EdgeInsets.only(bottom: 62.h)),
              SizedBox(
                width: 228.w,
                height: 47.h,
                child: Consumer(
                  builder: (context, ref, child) {
                    return GradientButton(
                      onPress: () {
                        var videoUrl = widget.creationResult.videoUrl ??
                            'https://alicdn.msmds.cn/text_to_video_demo.mp4';
                        ref
                            .read(creationVideoProvider.notifier)
                            .downloadVideo(videoUrl);
                      },
                      gradient: const LinearGradient(
                        colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                      ),
                      child: Text(
                        "保存至手机",
                        style: TextStyle(
                          fontSize: 18.sp,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
