import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/member/compute_power_provider.dart';
import 'package:text_generation_video/app/repository/modals/power/power_detail.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../widgets/appbar/leading.dart';

class ComputePowerDetailPage extends ConsumerWidget {
  const ComputePowerDetailPage({super.key});

  // item
  Widget _buildItem(PowerDetail detail) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 14, 20, 14),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${detail.description}",
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
              Text(
                "${detail.createTime}",
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          Text(
            "${detail.accountType == 1 ? '+' : '-'}${detail.powerNum}",
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "算力明细",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: CustomListView(
        onLoadMore: () async {
          ref.read(powerRecordProvider.notifier).loadMore();
        },
        data: ref.watch(
          powerRecordProvider.select((value) => value.powerDetailList),
        ),
        footerState: ref.watch(
          powerRecordProvider.select((value) => value.loadState),
        ),
        renderItem: (context, index, o) {
          return _buildItem(o);
        },
        separator: (context, index) {
          return const Divider(
            indent: 20,
            endIndent: 20,
            height: 0.8,
            color: Color(0xFFEFEFEF),
          );
        },
        empty: Container(
          margin: EdgeInsets.only(top: 120.h),
          alignment: Alignment.center,
          child: const Text(
            "暂无数据",
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ),
      ),
    );
  }
}
