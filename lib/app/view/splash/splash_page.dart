import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../config/global_config.dart';
import '../../../utils/prefs_util.dart';

/// app 闪屏页
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  SplashPageState createState() => SplashPageState();
}

class SplashPageState extends State<SplashPage> {
  final List<String> keys = ['《用户协议》', '《用户隐私政策》'];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _agreeDialog();
    });
  }

  /// 退出app
  void _appExit() {
    /// 暂时使用exit
    exit(0);
  }

  /// 同意协议
  void _agree() {
    GlobalConfig.agreePrivacy = true;
    PrefsUtil().setBool(PrefsKeys.agreePrivacy, true);
    context.replace('/');
  }

  /// keys点击
  void keysTap(String key) {
    if (key == '《用户协议》') {
    } else if (key == '《用户隐私政策》') {}
  }

  /// 同意按钮
  Widget _buildAgreeBtn() {
    return InkWell(
      onTap: () {
        SmartDialog.dismiss();
        _agree();
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 33.w),
        padding: EdgeInsets.symmetric(vertical: 15.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          gradient: const LinearGradient(
            colors: [
              Color(0xFFFE5640),
              Color(0xFFFA2E1B),
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
        ),
        child: Center(
          child: Text(
            '同意',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }

  /// 不同意按钮
  Widget _buildDisagreeBtn(bool first) {
    return InkWell(
      onTap: first ? _disagreeDialog : _appExit,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.h),
        child: Center(
          child: Text(
            '不同意',
            style: TextStyle(
              color: const Color(0xFF999999),
              fontSize: 14.sp,
            ),
          ),
        ),
      ),
    );
  }

  void _agreeDialog() {
    List<String> content = [
      "感谢您对返利快车一直以来的信任！\n",
      "我们非常重视您的个人信息和隐私安全。为了更好地保障您的个人利益，在您使用我们的产品前，请您务必认真阅读并透彻理解",
      "《用户协议》",
      "、",
      "《用户隐私政策》",
      "的全部内容,特别是以粗体标识的条款，您应重点阅读。特向您说明如下：\n",
      "1.为向您提供交易相关基本功能，我们会收集、使用必要的信息；\n",
      "2.我们会采取业界先进的安全措施保护您的信息安全；\n",
      "3.未经您同意，我们不会从第三方处获取、共享或向其提供您的信息，也不会将您的信息用于您未授权的其他用途或目的；\n",
      "4.您可以查询、更正、删除您的个人信息，我们也提供了注销账号的途径；\n",
      "5.基于您的明示授权，我们可能会获取您的位置（为您推荐附近的优惠加油站）、日历（设置快抢提醒闹钟或外卖提醒闹钟）、存储卡内容（保存商品分享图，使用客服时上传图片与视频）、粘贴板权限（智能识别购物软件复制的具体商品）、相机及麦克风（使用客服功能时上传视频与语音）、本机电话号码（为您提供本机号码一键快捷登录服务）、设备识别码/MAC地址/IMSI（用于账号信息验证以及通知推送功能）等信息，您有权拒绝或取消授权；\n",
      "6.为向您提供用户注册、查看商品浏览历史、查看会员开通记录等相关服务，我们会根据您使用服务的具体功能实现，需要收集必要的用户信息。我们会收集收藏及浏览记录在内的服务日志信息用于实现收藏记录及浏览足迹目的，为实现业务功能我们会记录返现订单信息及收款账号信息；\n\n",
      "请您仔细阅读隐私政策，确保对其内容已全部知晓并充分理解。如需帮助，可在工作日致电：020-87577697进行咨询。",
    ];

    SmartDialog.show(
      backType: SmartBackType.block,
      clickMaskDismiss: false,
      keepSingle: true,
      builder: (_) {
        return Center(
          child: Container(
            width: MediaQuery.of(context).size.width * .86,
            padding: EdgeInsets.only(top: 20.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "请阅读理解并同意以下协议",
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Container(
                  height: 228.h,
                  padding: EdgeInsets.only(top: 16.h),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: HighlightText(
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                        height: 1.6,
                      ),
                      data: content,
                      keys: keys,
                      keyStyle: TextStyle(
                        color: const Color(0xFFF93324),
                        fontSize: 14.sp,
                      ),
                      onTapCallback: (String key) {
                        keysTap(key);
                      },
                    ),
                  ),
                ),
                Padding(padding: EdgeInsets.only(bottom: 10.h)),
                _buildAgreeBtn(),
                _buildDisagreeBtn(true),
              ],
            ),
          ),
        );
      },
    );
  }

  void _disagreeDialog() {
    SmartDialog.dismiss();
    List<String> content = [
      "需同意",
      "《用户协议》",
      "和",
      "《用户隐私政策》",
      "才能继续",
    ];

    SmartDialog.show(
      backType: SmartBackType.block,
      clickMaskDismiss: false,
      keepSingle: true,
      builder: (_) {
        return Center(
          child: Container(
            width: MediaQuery.of(context).size.width * .86,
            padding: EdgeInsets.only(top: 20.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "温馨提示",
                  style: TextStyle(
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Container(
                  height: 80.h,
                  padding: EdgeInsets.only(top: 23.h),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    child: HighlightText(
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                        height: 1.6,
                      ),
                      data: content,
                      keys: keys,
                      keyStyle: TextStyle(
                        color: const Color(0xFFF93324),
                        fontSize: 14.sp,
                      ),
                      onTapCallback: (String key) {
                        keysTap(key);
                      },
                    ),
                  ),
                ),
                Padding(padding: EdgeInsets.only(bottom: 10.h)),
                _buildAgreeBtn(),
                _buildDisagreeBtn(false),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold();
  }
}
