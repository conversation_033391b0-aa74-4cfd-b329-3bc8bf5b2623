import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/app_purchase_provider.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/zenai_app_purchase_provider.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/constant.dart';
import '../../../navigation/router.dart';
import '../../../provider/member/member_provider.dart';

class MemberActionWidget extends ConsumerStatefulWidget {
  const MemberActionWidget({super.key});

  @override
  MemberActionWidgetState createState() => MemberActionWidgetState();
}

class MemberActionWidgetState extends ConsumerState<MemberActionWidget> {
  @override
  Widget build(BuildContext context) {
    var member = ref.watch(memberInfoProvider);
    var agreement = ref.watch(zenAiConsentAgreementProvider);
    var currentProduct = ref.watch(currentAppleProductProvider);
    return Column(
      children: [
        GradientButton(
          onPress: () {
            ref.read(zenAiAppPurchaseProvider.notifier).purchaseById();
          },
          enable: agreement && currentProduct != null,
          padding: const EdgeInsets.symmetric(vertical: 15),
          shadow: false,
          radius: 26,
          gradient: const LinearGradient(
            colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
          ),
          child: Text(
            "5折特惠·${member == null ? "立即抢购" : "恢复订阅"}",
            style: const TextStyle(fontSize: 16, color: Color(0xFF18161A)),
          ),
        ),
        const SizedBox(height: 12),
        InkWell(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: () {
            ref
                .read(zenAiConsentAgreementProvider.notifier)
                .setAgreement(!agreement);
          },
          child: Row(
            children: [
              Container(
                width: 16,
                height: 16,
                margin: const EdgeInsets.only(right: 12),
                decoration: BoxDecoration(
                  color: agreement
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF222123),
                  border: Border.all(color: Colors.white, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: agreement
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 12,
                      )
                    : null,
              ),
              RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF30E6B8),
                  ),
                  children: [
                    const TextSpan(
                      text: "同意",
                      style: TextStyle(color: Color(0xFF8A8D93)),
                    ),
                    TextSpan(
                      text: "《会员服务协议》",
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          context.push(
                            "/$webPage",
                            extra: {
                              "title": "",
                              "url": Constant.userAgreementUrl
                            },
                          );
                        },
                    ),
                    const TextSpan(
                      text: "和",
                      style: TextStyle(
                        color: Color(0xFF8A8D93),
                      ),
                    ),
                    TextSpan(
                      text: "《自动扣费服务协议》",
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          context.push(
                            "/$webPage",
                            extra: {
                              "title": "",
                              "url": Constant.memberPolicyUrl
                            },
                          );
                        },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
