import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MemberCounterDownWidget extends ConsumerStatefulWidget {
  const MemberCounterDownWidget({super.key});

  @override
  MemberCounterDownWidgetState createState() => MemberCounterDownWidgetState();
}

class MemberCounterDownWidgetState
    extends ConsumerState<MemberCounterDownWidget> {
  late Timer _timer;
  late Duration _remaining;

  @override
  void initState() {
    super.initState();
    _updateRemaining();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateRemaining();
    });
  }

  void _updateRemaining() {
    final now = DateTime.now();
    final midnight = DateTime(now.year, now.month, now.day, 23, 59, 59);
    var diff = midnight.difference(now);

    // 如果过了 23:59:59，目标切换到第二天的 23:59:59
    if (diff.isNegative) {
      final tomorrowMidnight =
          DateTime(now.year, now.month, now.day + 1, 23, 59, 59);
      diff = tomorrowMidnight.difference(now);
    }

    setState(() {
      _remaining = diff;
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  Widget _formatDuration(Duration d) {
    final days = d.inDays.toString();
    final hours = d.inHours.toString().padLeft(2, '0');
    final minutes = (d.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (d.inSeconds % 60).toString().padLeft(2, '0');
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildValueWidget(days, "天"),
        _buildValueWidget(hours, "时"),
        _buildValueWidget(minutes, "分"),
        _buildValueWidget(seconds, "秒"),
        const Text(
          "后恢复原价",
          style: TextStyle(
            fontSize: 12,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildValueWidget(String value, String timeText) {
    return Row(
      children: [
        Container(
          width: 22,
          height: 23,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: const Color(0xFFFD692F),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Text(
            value,
            style: const TextStyle(fontSize: 12, color: Colors.white),
          ),
        ),
        const SizedBox(width: 3),
        Text(
          timeText,
          style: const TextStyle(fontSize: 12, color: Colors.white),
        ),
        const SizedBox(width: 3),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 12),
      child: _formatDuration(_remaining),
    );
  }
}
