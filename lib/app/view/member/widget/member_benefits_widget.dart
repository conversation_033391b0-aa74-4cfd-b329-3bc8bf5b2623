import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/member/member_provider.dart';
import 'package:text_generation_video/app/repository/modals/member/member_benefits.dart';

import '../../../../config/constant.dart';
import '../../../../config/icon_address.dart';
import '../../../navigation/router.dart';

class MemberBenefitsWidget extends ConsumerWidget {
  const MemberBenefitsWidget({super.key});

  // 查看全部和权益对比
  Widget _buildShowMore(BuildContext context, String title) {
    return InkWell(
      onTap: () {
        context.push("/$webPage", extra: {
          "title": "",
          "url": Constant.memberBenefitsUrl,
        });
      },
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF94979D),
            ),
          ),
          const SizedBox(width: 3),
          const Icon(
            Icons.arrow_forward_ios_rounded,
            color: Color(0xFF94979D),
            size: 9,
          ),
        ],
      ),
    );
  }

  //
  Widget _buildItem(MemberBenefits benefits, double itemWidget) {
    return SizedBox(
      width: itemWidget,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CachedNetworkImage(imageUrl: benefits.iconUrl ?? "", width: 21),
          const SizedBox(height: 8),
          Text(
            "${benefits.rightName}",
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
          Text(
            "${benefits.rightDesc}",
            style: const TextStyle(
              fontSize: 10,
              color: Color(0xFF94979D),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var itemWidget = (MediaQuery.sizeOf(context).width - 32) / 4;
    return ref.watch(fetchMemberBenefitsListProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return const SizedBox(height: 20);
        }
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 22, 16, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  RichText(
                    text: const TextSpan(
                      children: [
                        TextSpan(text: "超"),
                        TextSpan(
                          text: "100+",
                          style: TextStyle(
                            color: Color(0xFF30E6B8),
                          ),
                        ),
                        TextSpan(text: "项VIP会员顶级权益"),
                      ],
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
                  ),
                  _buildShowMore(context, "权益对比"),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Wrap(
                runSpacing: 20,
                children: data.map((e) => _buildItem(e, itemWidget)).toList(),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildShowMore(context, "查看全部"),
                ],
              ),
            ),
          ],
        );
      },
      error: (o, s) {
        return const SizedBox(height: 20);
      },
      loading: () {
        return const SizedBox(height: 20);
      },
    );
  }
}
