import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/zenai_app_purchase_provider.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/icon_address.dart';
import '../../../provider/in_app_purchase/app_purchase_provider.dart';

class MemberInterestsWidget extends ConsumerWidget {
  const MemberInterestsWidget({super.key});

  Widget _buildStatus(
    BuildContext context,
    WidgetRef ref,
    ZenAiPurchaseData purchaseData,
  ) {
    if (purchaseData.status == 0) {
      return const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: Color(0xFF30E6B8),
        ),
      );
    }
    if (purchaseData.status == 1) {
      return SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        child: Row(
          spacing: 14,
          mainAxisAlignment: MainAxisAlignment.start,
          children: purchaseData.products
              .map(
                (e) => _memberInfoItem(ref, e, purchaseData.products.first),
              )
              .toList(),
        ),
      );
    }
    if (purchaseData.status == 2) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "${purchaseData.errMsg}",
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF94979D),
            ),
          ),
          const SizedBox(height: 6),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GradientButton(
                onPress: () {
                  ref
                      .read(zenAiAppPurchaseProductProvider.notifier)
                      .loadAllProducts();
                },
                shadow: false,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 3,
                ),
                gradient: const LinearGradient(
                  colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                ),
                child: const Text(
                  "重试",
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF18161A),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
    return const SizedBox();
  }

  // 会员信息项显示
  Widget _memberInfoItem(
    WidgetRef ref,
    CustomAppleProduct produce,
    CustomAppleProduct spec,
  ) {
    var currentProduct = ref.watch(currentAppleProductProvider);
    bool isSelect = produce.productId == currentProduct?.productId;
    var rawPrice = produce.details?.rawPrice;
    var days = produce.package?.durationDayNum;
    var dailyPrices = "";
    if (rawPrice != null && days != null && days != 0) {
      if (produce.productId == ApplePayProduct.weeklyMember.productId) {
        dailyPrices = (rawPrice / days).toStringAsFixed(2);
      } else if (produce.productId == ApplePayProduct.yearMember.productId) {
        dailyPrices = (rawPrice / days).toStringAsFixed(2);
      } else if (produce.productId == ApplePayProduct.monthlyMember.productId) {
        dailyPrices = (rawPrice / days).toStringAsFixed(2);
      }
    }
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.bottomCenter,
      children: [
        InkWell(
          onTap: () {
            ref
                .read(currentAppleProductProvider.notifier)
                .setCurrentProduct(produce);
          },
          child: Container(
            width: 105.w,
            padding: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E1F1F),
              border: Border.all(
                color: isSelect
                    ? const Color(0xFF30E6B8)
                    : const Color(0xFF1E1F1F),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(13),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  "${produce.package?.memberName}",
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic, // 基线对齐
                  children: [
                    Text(
                      "${produce.details?.currencySymbol}",
                      style: const TextStyle(
                        fontSize: 18,
                        color: Color(0xFF30E6B8),
                      ),
                    ),
                    const SizedBox(width: 3),
                    Text(
                      "${produce.details?.rawPrice}",
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF30E6B8),
                      ),
                    ),
                  ],
                ),
                Column(
                  children: [
                    if (produce.productId ==
                        ApplePayProduct.yearMember.productId)
                      RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            color: Color(0xFF8A8D93),
                          ),
                          children: [
                            const TextSpan(
                              text: "原价",
                              style: TextStyle(fontSize: 10),
                            ),
                            const WidgetSpan(child: SizedBox(width: 3)),
                            TextSpan(
                              text: "¥${produce.package?.originalPrice}",
                              style: const TextStyle(
                                fontSize: 12,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          ],
                        ),
                      ),
                    RichText(
                      text: TextSpan(
                        style: const TextStyle(
                          color: Color(0xFF8A8D93),
                        ),
                        children: [
                          TextSpan(
                            text: "${produce.details?.currencySymbol}",
                            style: const TextStyle(fontSize: 12),
                          ),
                          const WidgetSpan(child: SizedBox(width: 2)),
                          TextSpan(
                            text: "$dailyPrices/天",
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
        if (spec.productId == produce.productId)
          Positioned(
            top: -7,
            child: Container(
              padding: const EdgeInsets.fromLTRB(10, 3, 10, 3),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFFFD692F),
                    Color(0xFFFD692F),
                  ],
                ),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "新用户5折",
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTips(CustomAppleProduct? currentPurchase) {
    var currentPurchaseId = currentPurchase?.productId;
    String time = "";
    if (currentPurchaseId == ApplePayProduct.weeklyMember.productId) {
      time = "周";
    } else if (currentPurchaseId == ApplePayProduct.yearMember.productId) {
      time = "年";
    } else if (currentPurchaseId == ApplePayProduct.monthlyMember.productId) {
      time = "月";
    }
    return Text(
      "到期按${currentPurchase?.details?.currencySymbol}"
      "${currentPurchase?.details?.rawPrice}/$time"
      "自动续费，可随时取消，每30天登陆赠送600灵感值",
      style: const TextStyle(
        fontSize: 11,
        color: Color(0xFF505256),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var itemWidth = 105.w;
    var itemHeight = itemWidth * 122 / 105;
    var currentPurchase = ref.watch(currentAppleProductProvider);
    var purchaseData = ref.watch(zenAiAppPurchaseProductProvider);
    var status = purchaseData.status;
    return Column(
      children: [
        const SizedBox(height: 13),
        Image.asset(
          memberInterestsHead,
          width: MediaQuery.sizeOf(context).width,
        ),
        const SizedBox(height: 15),
        SizedBox(
          height: itemHeight,
          child: _buildStatus(context, ref, purchaseData),
        ),
        const SizedBox(height: 10),
        if (status == 1) _buildTips(currentPurchase),
        const SizedBox(height: 14),
        const Divider(color: Color(0xFF3C3B3B), height: 0.6),
      ],
    );
  }
}
