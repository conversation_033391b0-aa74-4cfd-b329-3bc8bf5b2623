import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:text_generation_video/app/provider/member/member_provider.dart';
import 'package:text_generation_video/app/repository/modals/member/member_banner.dart';

import '../../../../config/icon_address.dart';

class MemberBannerWidget extends ConsumerStatefulWidget {
  const MemberBannerWidget({super.key});

  @override
  MemberBannerWidgetState createState() => MemberBannerWidgetState();
}

class MemberBannerWidgetState extends ConsumerState<MemberBannerWidget> {
  final CarouselSliderController _carouselController =
      CarouselSliderController();

  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    double width = 375.w;
    double height = width * 385 / 375;
    return ref.watch(fetchMemberBannerListProvider).when(
      data: (data) {
        var list = data ?? [];
        return DefaultTabController(
          length: list.length,
          child: Stack(
            alignment: Alignment.bottomLeft,
            children: [
              CarouselSlider.builder(
                carouselController: _carouselController,
                itemCount: list.length,
                itemBuilder: (context, index, view) {
                  var item = list[index];
                  return Stack(
                    children: [
                      CachedNetworkImage(
                        imageUrl: item.bannerImgUrl ?? "",
                        fit: BoxFit.cover,
                      ),
                      Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0x00000000), Color(0xFF18161A)],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                    ],
                  );
                },
                options: CarouselOptions(
                    aspectRatio: width / height,
                    autoPlay: true,
                    enableInfiniteScroll: list.length > 1,
                    viewportFraction: 1,
                    onPageChanged: (index, reason) {
                      setState(() {
                        currentIndex = index;
                      });
                    }),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(left: 16),
                    child: AnimatedSmoothIndicator(
                      activeIndex: currentIndex,
                      count: list.length,
                      effect: ExpandingDotsEffect(
                        spacing: 2.0,
                        radius: 4.0,
                        dotWidth: 3.0,
                        dotHeight: 3.0,
                        expansionFactor: 3,
                        paintStyle: PaintingStyle.fill,
                        dotColor: Colors.white.withValues(alpha: 0.5),
                        activeDotColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: MemberBannerTab(
                      list: list,
                      currentIndex: currentIndex,
                      onTap: (index) {
                        _carouselController.animateToPage(index);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
      error: (o, s) {
        return SizedBox(width: width, height: height);
      },
      loading: () {
        return SizedBox(width: width, height: height);
      },
    );
  }
}

class MemberBannerTab extends ConsumerStatefulWidget {
  const MemberBannerTab({
    super.key,
    required this.list,
    required this.onTap,
    this.currentIndex = 0,
  });

  final List<MemberBanner> list;
  final Function(int) onTap;
  final int currentIndex;

  @override
  MemberBannerTabState createState() => MemberBannerTabState();
}

class MemberBannerTabState extends ConsumerState<MemberBannerTab> {
  // tab item
  Widget _buildTabItem(MemberBanner e) {
    double itemWidget = 64.w;
    double itemHeight = itemWidget * 75 / 64;
    bool select = widget.list[widget.currentIndex].id == e.id;
    return Container(
      width: itemWidget,
      height: itemHeight,
      decoration: BoxDecoration(
        color: const Color(0x20FFFFFF),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: select ? const Color(0xFFFFFFFF) : Colors.transparent,
          width: 0.6,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CachedNetworkImage(
            imageUrl: e.bannerIconUrl ?? "",
            width: 18,
            color: select ? Colors.white : const Color(0xFFC4C9D3),
          ),
          const SizedBox(height: 10),
          Text(
            "${e.bannerName}",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 10,
              color: select ? const Color(0xFFFFFFFF) : const Color(0xFFC4C9D3),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return TabBar(
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      dividerHeight: 0,
      labelPadding: const EdgeInsets.symmetric(horizontal: 4),
      indicator: const BoxDecoration(),
      tabs: widget.list.map((e) => _buildTabItem(e)).toList(),
      onTap: (index) {
        widget.onTap.call(index);
      },
    );
  }
}
