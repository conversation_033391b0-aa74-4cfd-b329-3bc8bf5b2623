import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/zenai_app_purchase_provider.dart';
import 'package:text_generation_video/app/view/member/widget/member_action_widget.dart';
import 'package:text_generation_video/app/view/member/widget/member_banner_widget.dart';
import 'package:text_generation_video/app/view/member/widget/member_benefits_widget.dart';
import 'package:text_generation_video/app/view/member/widget/member_counter_down_widget.dart';
import 'package:text_generation_video/app/view/member/widget/member_interests_widget.dart';
import '../../widgets/appbar/leading.dart';

class MemberPage extends ConsumerWidget {
  const MemberPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.transparent,
        toolbarHeight: 44.h,
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {
              ref.read(zenAiAppPurchaseProvider.notifier).resumePurchase();
            },
            child: const Text(
              "恢复购买",
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFFFFFFFF),
              ),
            ),
          ),
          const SizedBox(width: 21),
        ],
      ),
      body: Column(
        children: [
          const Expanded(
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: MemberBannerWidget(),
                ),
                SliverToBoxAdapter(
                  child: MemberInterestsWidget(),
                ),
                SliverToBoxAdapter(
                  child: MemberBenefitsWidget(),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const MemberCounterDownWidget(),
                const SizedBox(height: 13),
                const MemberActionWidget(),
                SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
