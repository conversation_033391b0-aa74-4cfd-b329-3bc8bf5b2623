import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/view/setting/dialog/logoff_dialog.dart';
import 'package:text_generation_video/config/icon_address.dart';

import '../../provider/account/auth_provider.dart';
import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: text_generation_video
/// @Package: app.view.setting
/// @ClassName: privacy_center_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/11 11:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/11 11:23
/// @UpdateRemark: 更新说明
class PrivacyCenterPage extends ConsumerWidget {
  const PrivacyCenterPage({super.key});

  Widget _buildItem(String icon, String title, Function()? onPress) {
    return InkWell(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Image.asset(
                  icon,
                  width: 24.w,
                  height: 24.h,
                ),
                Padding(padding: EdgeInsets.only(right: 12.w)),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: const Color(0xFFB8B4B4),
              size: 14.r,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    Widget logoffWidget = Container();
    if (userData != null) {
      logoffWidget = _buildItem(
        cancelAccount,
        "注销",
        () {
          LogoffDialog.showLogoff(() {
            ref.read(authProvider.notifier).logout();
            context.go("/");
          });
        },
      );
    }
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "隐私中心",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Column(
        children: [
          Padding(padding: EdgeInsets.only(top: 2.h)),
          _buildItem(userInfoIcon, "个人信息收集清单", () {
            context
                .go("/$settingPage/$privacyCenterPage/$collectionMessagePage");
          }),
          Divider(
            indent: 21.w,
            endIndent: 21.w,
            height: 1,
            color: const Color(0xFFEFEFEF),
          ),
          // _buildItem(sdkInfoIcon, "SDK共享清单", () {}),
          // Divider(
          //   indent: 21.w,
          //   endIndent: 21.w,
          //   height: 1,
          //   color: const Color(0xFFEFEFEF),
          // ),
          logoffWidget,
        ],
      ),
    );
  }
}
