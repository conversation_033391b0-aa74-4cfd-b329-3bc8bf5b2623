import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting.dialog
/// @ClassName: logout_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/11 11:44
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/11 11:44
/// @UpdateRemark: 更新说明
class LogoutDialog {
  static void showLogout(Function onPress) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: true,
      tag: "confirm_logout_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 23.h, bottom: 19.h),
                    child: Text(
                      "温馨提示",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                  Text(
                    "您确定退出账号吗？",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 30.h)),
                  Divider(
                    indent: 5.w,
                    endIndent: 5.w,
                    color: const Color(0xFFE6E6E6),
                    height: 1,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "confirm_logout_dialog");
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "取消",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 24.h,
                        color: const Color(0xFFE6E6E6),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "confirm_logout_dialog");
                            onPress();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "确认",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFF4C4C),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
