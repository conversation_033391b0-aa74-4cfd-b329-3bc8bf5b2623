import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting.dialog
/// @ClassName: logout_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/11 11:44
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/11 11:44
/// @UpdateRemark: 更新说明
class LogoffDialog {
  static void showLogoff(Function onPress) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: true,
      tag: "confirm_logoff_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 22.h, bottom: 12.h),
                    child: Text(
                      "确认注销？",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                  Text(
                    "无法再登录当前账号、",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  Text(
                    "账户信息将依法合规删除、",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  Text(
                    "注销行为无法撤销，无法恢复数据",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 21.h)),
                  Divider(
                    indent: 5.w,
                    endIndent: 5.w,
                    color: const Color(0xFFE6E6E6),
                    height: 1,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "confirm_logoff_dialog");
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "取消",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 24.h,
                        color: const Color(0xFFE6E6E6),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "confirm_logoff_dialog");
                            onPress();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "确认",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFF4C4C),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
