import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting
/// @ClassName: user_agreement_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/18 18:09
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/18 18:09
/// @UpdateRemark: 更新说明

class UserAgreementPage extends ConsumerWidget {
  const UserAgreementPage({super.key});

  Widget _buildItem(String title, String value) {
    return Padding(
      padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 5.h)),
          Text(
            value,
            style: TextStyle(
              fontSize: 15.sp,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "用户协议",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 10.h, 12.w, 6.h),
              child: Text(
                "更新日期：2025年3月1日\n生效日期：2025年3月1日",
                style: TextStyle(
                  fontSize: 15.sp,
                  color: const Color(0xFF333333),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 10.h),
              child: Text(
                "欢迎您使用ZenAi APP！本应用由北京顶云网络有限公司（以下简称“我们”）开发与运营。在您使用本应用前，请您仔细阅读并完全理解本用户协议（以下简称“本协议”）的所有内容，特别是加粗的条款。本协议旨在规范用户（以下简称“您”）与我们之间的权利义务。使用本应用即表示您同意本协议的条款。",
                style: TextStyle(
                  fontSize: 15.sp,
                  color: const Color(0xFF333333),
                ),
              ),
            ),
            _buildItem(
              "一、协议的接受",
              "1. 您通过网络页面点击确认或以其他方式选择接受本协议，即表示您已仔细阅读并明示并无条件接受本协议及我们随时更新的各类规则。\n2. 我们有权根据需要不时修改本协议或各类规则，修改后的协议一旦被张贴在本应用上即生效，并替代原来的协议。您可随时登录查阅最新协议；您继续使用本应用将表示您接受改动后的协议。",
            ),
            _buildItem(
              "二、账号注册与使用",
              "1. 用户注册成功后，我们将给予每个用户一个用户账户及相应的密码，该用户账户和密码由用户负责保管；用户应当对以其用户账户进行的所有活动和事件负法律责任。\n2. 用户须对在本应用的注册信息的真实性、合法性、有效性承担全部责任，任何原因导致的您的注册信息不真实、不准确或不完整，需要自行承担由此引发的相应责任与后果。\n3. 用户不得将本应用的账户、密码等账户信息提供给他人使用，如用户发现其账户遭他人非法使用或存在安全漏洞的情况，应立即通知我们。\n4. 我们不对外公开或向第三方提供单个用户的注册资料，除非：\n- 事先获得用户的明确授权；\n- 只有披露您的个人资料，才能提供您所要求的产品和服务；\n- 根据有关的法律法规要求；\n- 按照相关政府主管部门的要求。",
            ),
            _buildItem(
              "三、用户个人隐私信息保护",
              "1. 保护用户个人隐私是我们的一项基本政策，我们保证不会公开、编辑或透露用户的注册资料及保存在本应用中的非公开内容，除非有法律许可要求或本协议另有规定。\n2. 在您注册本应用账户，使用本应用产品或服务，或访问本应用网页时，我们自动接收并记录的您的浏览器和计算机上的信息包括但不限于您的IP地址、浏览器类型、使用的语言以及访问日期和时间等数据。",
            ),
            _buildItem(
              "四、用户义务",
              "1. 不得利用本应用制作、上传、复制、发布、传播或者转载如下内容：\n- 反对宪法所确定的基本原则的；\n- 危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；\n- 损害国家荣誉和利益的；\n- 煽动民族仇恨、民族歧视，破坏民族团结的；\n- 破坏国家宗教政策，宣扬邪教和封建迷信的；\n- 散布谣言，扰乱社会秩序，破坏社会稳定的；\n- 散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；\n- 侮辱或者诽谤他人，侵害他人合法权益的；\n- 含有法律、行政法规禁止的其他内容的。",
            ),
            _buildItem(
              "五、版权声明",
              "1. 本应用的所有内容，包括但不限于文本、图形、LOGO、图像、视频、软件、程序、以及服务名称、商标和著作权，都是北京顶云网络有限公司的财产，受到著作权法、商标法和/或其他财产所有权法的保护。\n2. 未经我们明确的书面同意，任何单位或个人不得复制、修改、传播、参与传播或以任何其他方式使用上述资料。",
            ),
            _buildItem(
              "六、法律责任与免责",
              "1. 用户明确同意其使用本应用网络服务所存在的风险将完全由其本人承担；因其使用本应用网络服务而产生的一切后果也由其本人承担，我们不承担任何责任。\n2. 我们不保证网络服务一定满足用户的使用需求，也不保证网络服务不会被中断，对网络服务的及时性、安全性、准确性也不作保证。",
            ),
            _buildItem(
              "七、协议修改",
              "1. 我们有权在必要时修改本协议条款，您可以在本应用的公告处查阅最新版协议条款。\n2. 如果不同意我们对本协议条款所做的修改，用户有权并应当立即停止使用本应用。如果用户继续使用本应用，则视为接受我们对本协议条款的修改。",
            ),
            _buildItem(
              "八、通知送达",
              "1. 本协议项下所有的通知均可通过重要页面的公告或用户的注册邮箱或您提供的通讯地址传送，通知自发送之日起视为已送达。",
            ),
            _buildItem(
              "九、如何联系我们",
              "如您对本协议或本应用有任何问题，请联系我们的客服邮箱：<EMAIL>\n\n公司地址：北京市房山区青龙湖镇大苑村街南146号1号楼三层1157\n\n感谢您选择ZenAi APP。希望我们的服务能让您的生活更加丰富多彩！",
            ),
            Padding(
              padding: EdgeInsets.only(top: 8.h, bottom: 21.h, right: 12.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    "北京顶云网络有限公司",
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: const Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
