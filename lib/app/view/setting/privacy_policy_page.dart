import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting
/// @ClassName: collect_message_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/18 17:29
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/18 17:29
/// @UpdateRemark: 更新说明

class PrivacyPolicyPage extends ConsumerWidget {
  const PrivacyPolicyPage({super.key});

  Widget _buildItem(String title, String value) {
    return Padding(
      padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 5.h)),
          Text(
            value,
            style: TextStyle(
              fontSize: 15.sp,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "隐私政策",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 10.h, 12.w, 6.h),
              child: Text(
                "更新日期：2025年3月1日\n生效日期：2025年3月1日",
                style: TextStyle(
                  fontSize: 15.sp,
                  color: const Color(0xFF333333),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 10.h),
              child: Text(
                "欢迎您使用ZenAi APP（以下简称“本应用”），本应用由北京顶云网络有限公司（以下简称“我们”）开发与运营。我们非常重视您的个人隐私和个人信息保护。在您使用本应用服务前，请您仔细阅读并充分理解本《隐私政策》（以下简称“本政策”），特别是加粗的内容。我们将按照本政策收集、使用、存储和分享您的相关信息，并为您提供相关的隐私保护措施。 ",
                style: TextStyle(
                  fontSize: 15.sp,
                  color: const Color(0xFF333333),
                ),
              ),
            ),
            _buildItem(
              "一、适用范围",
              "本政策适用于ZenAi APP的所有相关服务。需要说明的是，本政策不适用于其他第三方向您提供的服务，例如第三方网站链接至本应用的服务。",
            ),
            _buildItem(
              "二、信息的收集",
              "1. 个人信息：您在注册或使用本应用时，需要提供的个人信息，包括但不限于您的姓名、电子邮件地址、电话号码等。\n2. 设备信息：系统自动收集的关于您的设备的信息，如设备型号、操作系统版本、设备设置、唯一设备标识符等。\n3. 日志信息：您使用本应用时，系统可能会自动接收并记录的您的浏览器和电脑上的信息，包括但不限于您的IP地址、浏览器类型、使用的语言、访问日期和时间、软件和硬件特征信息及您需求的网页记录等。\n4. 位置信息（仅在您同意的情况下收集）：当您开启设备定位功能并使用本应用相关服务时，我们可能会收集您的地理位置信息。",
            ),
            _buildItem(
              "三、信息的使用",
              "我们收集您的信息是为了提供服务及改善服务质量，具体用途包括：\n1. 提供服务：使用您的信息进行本应用服务的维护、提供、开发及改善。\n2. 产品开发和服务优化：关于本应用的使用情况统计、分析和应用研究等。\n3. 客户服务支持：根据您的信息，与您联系，解决您使用本应用过程中遇到的问题，包括提问响应及技术支持等。\n4. 安全保护：用于验证身份、预防、调查可能的欺诈、侵权、非法或不符合我们政策、协议或规则的行为以保护您、其他用户或我们的合法权益。",
            ),
            _buildItem(
              "四、信息的共享与披露",
              "除以下情形外，未经您同意，我们不会与任何第三方分享您的个人信息：\n1. 与国家法律法规要求或应司法机关、行政机关的要求进行提供。\n2. 与完全履行我们在本政策中说明的职责和提供您所需服务必要的合作伙伴分享，例如提供基础设施技术服务的供应商等。\n3. 只有共享您的信息，才能提供您需要的服务，或处理您与他人的纠纷或争议。例如，在您使用本应用提供的信息发布功能时，其他用户可以看到的公开信息。\n4. 您自行向第三方共享或公开：例如您在使用本应用时自行上传的信息。",
            ),
            _buildItem(
              "五、信息的存储与安全",
              "我们采取各种合理的物理、管理和技术措施来保护您的信息，防止信息的丢失、不当使用、未经授权访问或披露。例如，我们使用加密技术确保数据的安全性；采用限制访问措施控制信息访问的权限。您的个人信息将被存储在中华人民共和国境内。如涉及跨境传输的，我们将按照国家法律法规的要求，确保您的个人信息受到足够的保护。",
            ),
            _buildItem(
              "六、您的权利",
              "在使用本应用服务过程中，您有权执行以下权利：\n1. 查询、更正、删除您的个人信息。\n2. 更改您授权同意的范围或撤回您的授权。\n3. 取消账户。\n4. 注销账户：您可以通过以下路径注销您的账户：“我的-右上角的设置-隐私中心-注销”。在您注销账户后，我们将停止为您提供本应用的全部产品和/或服务，并依据法律法规相关要求通过匿名化的方式处理您的个人信息，或删除与您账号相关的个人信息，但法律法规或监管机构对用户信息存储时间另有规定的除外。\n5. 若您认为您的个人信息被滥用或权利被侵害，可以联系我们进行投诉解决。",
            ),
            _buildItem(
              "七、未成年人的特别注意事项",
              "若您未满18周岁，您需在父母或监护人的指导下阅读本政策并使用本应用。我们将依据国家相关法律法规保护未成年人的个人信息。",
            ),
            _buildItem(
              "八、本政策的更改",
              "我们可能定期更新本政策以反映公司政策的变更或数据保护法的更新。如果我们对政策做出任何重要更改，我们会通过本应用或通过您提供的联系方式发布更新通知。",
            ),
            _buildItem(
              "九、如何联系我们",
              "如果您对本政策有任何疑问或关注，或需要与我们联系，请通过以下方式与我们联系：\n\n- 邮箱：<EMAIL>\n- 公司地址：北京市房山区青龙湖镇大苑村街南146号1号楼三层1157\n\n感谢您阅读我们的隐私政策。希望您享受使用ZenAi APP！",
            ),
            Padding(
              padding: EdgeInsets.only(top: 8.h, bottom: 21.h, right: 12.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    "北京顶云网络有限公司",
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: const Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
