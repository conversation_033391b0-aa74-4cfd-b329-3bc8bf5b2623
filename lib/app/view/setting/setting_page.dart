import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/view/setting/dialog/logout_dialog.dart';
import 'package:text_generation_video/config/constant.dart';
import 'package:text_generation_video/utils/platform_util.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../widgets/appbar/leading.dart';
import 'dialog/logoff_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting
/// @ClassName: setting_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/11 11:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/11 11:23
/// @UpdateRemark: 更新说明
class SettingPage extends ConsumerWidget {
  const SettingPage({super.key});

  Widget _buildItem(String title, Widget sub, Function() onPress) {
    return InkWell(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
              ),
            ),
            Row(
              children: [
                sub,
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: const Color(0xFFB8B4B4),
                  size: 14.r,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _logoffWidget(WidgetRef ref, BuildContext context) {
    var userData = ref.watch(authProvider);
    Widget logoffWidget = Container();
    if (userData != null) {
      logoffWidget = Column(
        children: [
          Divider(
            indent: 21.w,
            endIndent: 21.w,
            height: 1,
            color: const Color(0xFFEFEFEF),
          ),
          _buildItem(
            "注销账号",
            const SizedBox(),
            () {
              LogoffDialog.showLogoff(() {
                ref.read(authProvider.notifier).logout();
                context.go("/");
              });
            },
          )
        ],
      );
    }
    return logoffWidget;
  }

  /// 备案查询
  Widget _icpQueryWidget() {
    if (PlatformUtils.isAndroid) {
      return const SizedBox();
    }
    return Container(
      margin: EdgeInsets.only(bottom: 32.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "APP备案号：",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF333333),
            ),
          ),
          InkWell(
            onTap: () async {
              await launchUrl(
                Uri.parse(Constant.icpQueryUrl),
              );
            },
            child: Text(
              "京ICP备18034777号-4A",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.blueAccent,
                decoration: TextDecoration.underline,
                decorationColor: Colors.blueAccent,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    Widget logoutWidget = Container();
    if (userData != null) {
      logoutWidget = Container(
        width: 228.w,
        height: 47.h,
        margin: EdgeInsets.only(
          bottom: 70.h + MediaQuery.paddingOf(context).bottom,
        ),
        child: GradientButton(
          onPress: () {
            LogoutDialog.showLogout(
              () {
                ref.read(authProvider.notifier).logout();
                context.pop();
              },
            );
          },
          gradient: const LinearGradient(
            colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
          ),
          child: Text(
            "退出登录",
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.white,
            ),
          ),
        ),
      );
    }
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "设置",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Column(
        children: [
          Expanded(
            child: Column(
              children: [
                Padding(padding: EdgeInsets.only(top: 4.h)),
                _buildItem(
                  "用户协议",
                  Container(),
                  () {
                    context.push(
                      "/$webPage",
                      extra: {
                        "title": "用户协议",
                        "url": Constant.userAgreementUrl
                      },
                    );
                  },
                ),
                Divider(
                  indent: 21.w,
                  endIndent: 21.w,
                  height: 1,
                  color: const Color(0xFFEFEFEF),
                ),
                if (PlatformUtils.isIOS)
                  _buildItem(
                    "会员自动续费协议",
                    Container(),
                    () {
                      context.push(
                        "/$webPage",
                        extra: {
                          "title": "会员自动续费协议",
                          "url": Constant.memberPolicyUrl
                        },
                      );
                    },
                  ),
                if (PlatformUtils.isIOS)
                  Divider(
                    indent: 21.w,
                    endIndent: 21.w,
                    height: 1,
                    color: const Color(0xFFEFEFEF),
                  ),
                _buildItem(
                  "用户隐私政策",
                  const SizedBox(),
                  () {
                    context.push(
                      "/$webPage",
                      extra: {
                        "title": "用户隐私政策",
                        "url": Constant.privacyPolicyUrl
                      },
                    );
                  },
                ),
                Divider(
                  indent: 21.w,
                  endIndent: 21.w,
                  height: 1,
                  color: const Color(0xFFEFEFEF),
                ),
                _buildItem("个人信息收集清单", const SizedBox(), () {
                  context.go("/$settingPage/$collectionMessagePage");
                }),
                Divider(
                  indent: 21.w,
                  endIndent: 21.w,
                  height: 1,
                  color: const Color(0xFFEFEFEF),
                ),
                _buildItem(
                  "关于我们",
                  Container(),
                  () {
                    context.push("/$aboutUsPage");
                  },
                ),
                _logoffWidget(ref, context),
              ],
            ),
          ),
          _icpQueryWidget(),
          logoutWidget,
        ],
      ),
    );
  }
}
