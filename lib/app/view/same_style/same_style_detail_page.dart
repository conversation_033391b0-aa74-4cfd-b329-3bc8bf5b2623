import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/widgets/pageview/aware_page_view.dart';
import 'package:ui_widgets/ui_widgets.dart';

class SameStyleDetailPage extends ConsumerStatefulWidget {
  const SameStyleDetailPage({super.key, this.data});

  final Map? data;

  @override
  SameStyleDetailPageState createState() => SameStyleDetailPageState();
}

class SameStyleDetailPageState extends ConsumerState<SameStyleDetailPage> {
  @override
  Widget build(BuildContext context) {
    var tag = widget.data?["tag"];
    var data = [1, 23, 4, 5];
    return Hero(
      tag: tag,
      child: Scaffold(
        backgroundColor: const Color(0xFF18161A),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(
                      height: 500,
                      child: AwarePageView(
                        itemCount: data.length,
                        itemBuilder: (context, index) {
                          return Container(
                            color: index % 2 == 0 ? Colors.amber : Colors.red,
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 13),
                    Row(
                      children: [
                        const SizedBox(width: 16),
                        Container(
                          width: 18,
                          height: 18,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 5),
                        const Text(
                          "李大力",
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF8A8D93),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1F1F21),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "创作信息",
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                  color: Color(0xFFFFFFFF),
                                ),
                              ),
                              Text(
                                "图生视频",
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                  color: Color(0xFFFFFFFF),
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 6),
                          Text(
                            "我虽生在富贵人家，可我娘却是青楼女子，我爹让她怀孕后便娶了她当小妾。成婚当晚，我娘就被大夫人灌了一剂猛药，她想要一尸两命，可我却没死成，而是被提前催生了下来。我虽只是个女儿家，可大夫人依旧如临大敌，因为大夫人嫁给我爹3年无所出。一个青楼出身的小妾，比正头娘子先生孩子，不就是在打他的脸，看他的笑话吗?若非我身体里流着我爹的血，只怕他早就把我杀了。",
                            textAlign: TextAlign.justify,
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF8A8D93),
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            "我虽生在富贵人家，可我娘却是青楼女子，我爹让她怀孕后便娶了她当小妾。成婚当晚，我娘就被大夫人灌了一剂猛药，她想要一尸两命，可我却没死成，而是被提前催生了下来。我虽只是个女儿家，可大夫人依旧如临大敌，因为大夫人嫁给我爹3年无所出。一个青楼出身的小妾，比正头娘子先生孩子，不就是在打他的脸，看他的笑话吗?若非我身体里流着我爹的血，只怕他早就把我杀了。",
                            textAlign: TextAlign.justify,
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF8A8D93),
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            "我虽生在富贵人家，可我娘却是青楼女子，我爹让她怀孕后便娶了她当小妾。成婚当晚，我娘就被大夫人灌了一剂猛药，她想要一尸两命，可我却没死成，而是被提前催生了下来。我虽只是个女儿家，可大夫人依旧如临大敌，因为大夫人嫁给我爹3年无所出。一个青楼出身的小妾，比正头娘子先生孩子，不就是在打他的脸，看他的笑话吗?若非我身体里流着我爹的血，只怕他早就把我杀了。",
                            textAlign: TextAlign.justify,
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF8A8D93),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            _buildBottomAction(context),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomAction(BuildContext context) {
    return Container(
      color: const Color(0xFF18161A),
      padding: EdgeInsets.only(
        top: 10,
        bottom: MediaQuery.paddingOf(context).bottom + 20,
      ),
      child: Row(
        children: [
          const SizedBox(width: 16),
          SizedBox(
            width: 109.w,
            height: 51,
            child: GradientButton(
              onPress: () {
                context.pop();
              },
              radius: 16,
              border: Border.all(color: const Color(0xFF646464), width: 0.2),
              shadow: false,
              gradient: const LinearGradient(
                colors: [Color(0xFF29282B), Color(0xFF29282B)],
              ),
              child: const Text(
                "返回",
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF32E7BB),
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: SizedBox(
              height: 51,
              child: GradientButton(
                onPress: () {
                  context.pop();
                },
                radius: 16,
                shadow: false,
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF53F9DF),
                    Color(0xFF40EFCA),
                    Color(0xFF34E8BC),
                    Color(0xFF30E6B8),
                  ],
                  stops: [0.0, 0.1, 0.3, 1.0],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                child: const Text(
                  "做同款",
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF18161A),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
    );
  }
}
