import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.home.widget
/// @ClassName: main_show_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/10 15:15
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/10 15:15
/// @UpdateRemark: 更新说明

class MainShowWidget extends StatefulWidget {
  const MainShowWidget({super.key});

  @override
  MainShowWidgetState createState() => MainShowWidgetState();
}

class MainShowWidgetState extends State<MainShowWidget> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();
    var uri = Uri.parse(
      'https://alicdn.msmds.cn/text_to_video_demo.mp4',
    );
    _controller = VideoPlayerController.networkUrl(uri)
      ..initialize().then(
        (_) {
          // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
          debugPrint("initialize success------");
          setState(() {});
        },
      ).catchError(
        (e) {
          debugPrint('catchError: $e');
        },
      )
      ..setLooping(true)
      ..play();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildVideo() {
    Widget child;
    debugPrint("isInitialized: ${_controller.value.isInitialized}");
    if (_controller.value.isInitialized) {
      debugPrint("isInitialized: ${_controller.value.aspectRatio}");
      var aspectRatio = _controller.value.aspectRatio;
      double width;
      double height;
      if (aspectRatio > 1) {
        /// 宽高比大于1
        width = aspectRatio * 295.w;
        height = 295.w;
      } else {
        width = 295.w;
        height = aspectRatio * 295.w;
      }
      child = FittedBox(
        fit: BoxFit.cover,
        child: SizedBox(
          width: width,
          height: height,
          child: VideoPlayer(_controller),
        ),
      );
    } else {
      child = const Center(
        child: CircularProgressIndicator(
          color: Colors.green,
          strokeWidth: 1,
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(14.r),
      child: Container(
        width: 295.w,
        height: 295.w,
        decoration: const BoxDecoration(
          color: Colors.black,
        ),
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 295.w,
      height: 307.w,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Positioned(
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF4FAF3),
                borderRadius: BorderRadius.circular(14.r),
              ),
              width: 265.w,
              height: 281.w,
            ),
          ),
          Positioned(
            bottom: 6.h,
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFD8ECD4),
                borderRadius: BorderRadius.circular(14.r),
              ),
              width: 285.w,
              height: 281.w,
            ),
          ),
          _buildVideo(),
        ],
      ),
    );
  }
}
