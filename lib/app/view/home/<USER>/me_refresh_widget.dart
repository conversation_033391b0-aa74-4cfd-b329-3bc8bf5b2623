import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/utils/router_util.dart';

import '../../../../config/icon_address.dart';
import '../../../provider/digital/digital_video_provider.dart';

class MeRefreshWidget extends ConsumerStatefulWidget {
  const MeRefreshWidget({super.key});

  @override
  MeRefreshWidgetState createState() => MeRefreshWidgetState();
}

class MeRefreshWidgetState extends ConsumerState<MeRefreshWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  static const Duration minSpinDuration = Duration(seconds: 1); // 最少转一圈的时间

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    ); // 先准备好，但默认不运行

    _controller.stop(); // 默认停止
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        RouterUtil.checkLogin(context, call: () async {
          _controller.repeat();

          final stopwatch = Stopwatch()..start(); // ⏱️ 计时器
          await ref.read(customizeVideoRecordProvider.notifier).loadData(
                showToast: true,
              );
          final elapsed = stopwatch.elapsed;
          final remaining = minSpinDuration - elapsed;

          // ⏳ 如果请求时间不足一圈，等够时间再停
          if (remaining > Duration.zero) {
            await Future.delayed(remaining);
          }

          _controller.stop();
        });
      },
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.rotate(
            angle: _controller.value * 2 * pi,
            child: child,
          );
        },
        child: Image.asset(meRefreshBtn, width: 48, height: 48),
      ),
    );
  }
}
