import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/digital/digital_provider.dart';
import 'package:text_generation_video/app/view/home/<USER>/digital_item_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/digital_tab_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

class DigitalHumanWidget extends ConsumerStatefulWidget {
  const DigitalHumanWidget({super.key});

  @override
  DigitalHumanWidgetState createState() => DigitalHumanWidgetState();
}

class DigitalHumanWidgetState extends ConsumerState<DigitalHumanWidget> {
  late StreamSubscription<List<ConnectivityResult>> subscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (d) {
        subscription = Connectivity().onConnectivityChanged.listen(
          (List<ConnectivityResult> result) {
            debugPrint("onConnectivityChanged: ${result.length}");
            debugPrint("onConnectivityChanged: ${result.first}");
            final hasNetwork = result.any((r) => r != ConnectivityResult.none);
            if (hasNetwork) {
              debugPrint("onConnectivityChanged: refresh");
              ref.read(publicDigitalFilterListProvider.notifier).getFilter();
              ref.read(publicDigitalHumanListProvider.notifier).loadData();
            }
          },
        );
      },
    );
  }

  @override
  void dispose() {
    subscription.cancel();
    super.dispose();
  }

  // creation header
  Widget _buildHeadBanner() {
    return SliverToBoxAdapter(
      child: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 43),
            child: Image.asset(digitalHumanBg),
          ),
          const Positioned(
            left: 20,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "数字人视频",
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 33,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  "Ai自动对口型，输入文案一键成片",
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Color(0xFF00CF6F),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 3,
            left: 0,
            right: 0,
            child: InkWell(
              onTap: () {
                context.push("/$makeVideoDigitalPage");
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                padding:
                    const EdgeInsets.symmetric(vertical: 21, horizontal: 28),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  gradient: const LinearGradient(
                    stops: [0.0, 0.3, 0.7, 1.0],
                    colors: [
                      Color(0xFF1CFF73),
                      Color(0xFF75F598),
                      Color(0xFFA7FFA4),
                      Color(0xFF56F0D5),
                    ],
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "创作",
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF141414),
                          ),
                        ),
                        SizedBox(height: 3),
                        Text(
                          "数字人视频",
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF141414),
                          ),
                        ),
                      ],
                    ),
                    Image.asset(digitalAddIcon, width: 30, height: 30),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // customization
  // Widget _buildCustomization() {
  //   return SliverPadding(
  //     padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
  //     sliver: SliverToBoxAdapter(
  //       child: Row(
  //         children: [
  //           Expanded(
  //             child: InkWell(
  //               onTap: () {
  //                 context.push("/$customDigitalPage");
  //               },
  //               child: Container(
  //                 height: 87,
  //                 decoration: BoxDecoration(
  //                   color: const Color(0xFF1D272B),
  //                   borderRadius: BorderRadius.circular(12),
  //                 ),
  //                 child: Column(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     Image.asset(customizationDigitalIcon, width: 28),
  //                     const SizedBox(height: 12),
  //                     const Text(
  //                       "定制数字人",
  //                       style: TextStyle(
  //                         fontSize: 12,
  //                         color: Colors.white,
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //           ),
  //           const SizedBox(width: 10),
  //           Expanded(
  //             child: Container(
  //               height: 87,
  //               decoration: BoxDecoration(
  //                 color: const Color(0xFF1D272B),
  //                 borderRadius: BorderRadius.circular(12),
  //               ),
  //               child: Column(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 children: [
  //                   Image.asset(customizationVoiceIcon, width: 66),
  //                   const SizedBox(height: 5),
  //                   const Text(
  //                     "声音克隆",
  //                     style: TextStyle(
  //                       fontSize: 12,
  //                       color: Colors.white,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    var crossAxisCount = 3;
    var itemWidth = (MediaQuery.sizeOf(context).width - 40) / crossAxisCount;
    return CustomListView(
      sliverHeader: [
        _buildHeadBanner(),
        // _buildCustomization(),
        SliverToBoxAdapter(
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 12, left: 10),
                child: Image.asset(
                  digitalPublicHeaderIcon,
                  width: 83,
                  height: 26,
                ),
              ),
            ],
          ),
        ),
        const DigitalTabWidget(),
      ],
      data: ref.watch(
        publicDigitalHumanListProvider
            .select((value) => value.publicDigitalList),
      ),
      onLoadMore: () async {
        ref.read(publicDigitalHumanListProvider.notifier).loadMore();
      },
      footerState: ref.watch(
        publicDigitalHumanListProvider.select((value) => value.loadState),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10),
      sliverGridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        childAspectRatio: itemWidth / (itemWidth + 33),
      ),
      renderItem: (context, index, o) {
        return DigitalItemWidget(
          width: itemWidth,
          height: itemWidth + 33,
          publicDigitalHuman: o,
        );
      },
      empty: Container(
        margin: const EdgeInsets.only(top: 120),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "暂无数据",
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
