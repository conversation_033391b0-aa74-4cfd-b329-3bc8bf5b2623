import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/repository/modals/digital/public_digital_human.dart';

class DigitalItemWidget extends ConsumerWidget {
  const DigitalItemWidget({
    super.key,
    required this.width,
    required this.height,
    required this.publicDigitalHuman,
  });

  final double width;
  final double height;
  final PublicDigitalHuman publicDigitalHuman;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: () {
        context.push("/$publicDigitalDetailPage", extra: publicDigitalHuman);
      },
      child: SizedBox(
        width: width,
        height: height,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: width,
                height: width,
                decoration: const BoxDecoration(
                  color: Color(0xFF1D272B),
                ),
                child: CachedNetworkImage(
                  imageUrl: "${publicDigitalHuman.cover}",
                  width: width,
                  height: width,
                  errorWidget: (context, url, o) {
                    return SizedBox(
                      width: width,
                      height: height,
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 20,
              child: Text(
                "${publicDigitalHuman.personName}",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF94979D),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
