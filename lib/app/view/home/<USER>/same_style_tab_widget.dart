import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SameStyleTabWidget extends ConsumerStatefulWidget {
  const SameStyleTabWidget({super.key});

  @override
  SameStyleTabWidgetState createState() => SameStyleTabWidgetState();
}

class SameStyleTabWidgetState extends ConsumerState<SameStyleTabWidget> {
  int currentTab = 0;

  Widget _buildItemTab(
    BuildContext context,
    SameStyleTab item,
    List<SameStyleTab> tabs,
  ) {
    // bool isActive = tabs[currentTab].id == item.id;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 12),
      // margin: const EdgeInsets.symmetric(horizontal: 5),
      // decoration: BoxDecoration(
      //   gradient: isActive
      //       ? const LinearGradient(
      //           colors: [Color(0xFFA7FF58), Color(0xFF00D572)])
      //       : const LinearGradient(
      //           colors: [Color(0xFF273337), Color(0xFF273337)]),
      //   borderRadius: BorderRadius.circular(8),
      // ),
      child: Row(
        children: [
          if (item.icon != null) Icon(item.icon),
          Text(item.name ?? "")
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var tabs = [
      SameStyleTab(0, name: "热门", icon: Icons.add),
      SameStyleTab(1, name: "数字人"),
      SameStyleTab(2, name: "打工猫", icon: Icons.add),
      SameStyleTab(3, name: "故事小说"),
      SameStyleTab(4, name: "变身", icon: Icons.add),
      SameStyleTab(5, name: "测试111", icon: Icons.add),
      SameStyleTab(6, name: "测试222", icon: Icons.add),
      SameStyleTab(7, name: "测试333", icon: Icons.add),
      SameStyleTab(8, name: "测试444", icon: Icons.add),
      SameStyleTab(9, name: "测试555", icon: Icons.add),
      SameStyleTab(10, name: "测试666", icon: Icons.add),
    ];
    // if (tabs == null || tabs.isEmpty) {
    //   return const SliverToBoxAdapter(child: SizedBox());
    // }
    return DefaultTabController(
      length: tabs.length,
      child: SliverLayoutBuilder(
        builder: (context, constraints) {
          return SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.only(top: 14, bottom: 16),
              child: TabBar(
                isScrollable: true,
                tabAlignment: TabAlignment.start,
                dividerHeight: 0,
                indicator: const BoxDecoration(),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: const Color(0xFFFFFFFF),
                unselectedLabelColor: const Color(0xFF8A8D93),
                labelPadding: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(horizontal: 4),
                tabs: tabs
                    .map(
                      (e) => _buildItemTab(context, e, tabs),
                    )
                    .toList(),
                onTap: (index) {
                  debugPrint("index: $index");
                  setState(() {
                    currentTab = index;
                  });
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

class SameStyleTab {
  int id;
  String? name;
  IconData? icon;

  SameStyleTab(this.id, {this.name, this.icon});
}
