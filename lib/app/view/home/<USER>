import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/zenai_app_purchase_provider.dart';
import 'package:text_generation_video/app/view/home/<USER>/glide_me_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/glide_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/home_frame_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/portfolio_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/same_style_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart' as ui_widgets;

import '../../lifecycle/app_lifecycle_observer.dart';
import '../../provider/home/<USER>';
import '../../provider/in_app_purchase/app_purchase_provider.dart';
import '../../widgets/tab/tab_indicator.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: home_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/8 15:39
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/8 15:39
/// @UpdateRemark: 更新说明

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends ConsumerState<HomePage> {
  final NeverScrollableScrollPhysics _scrollPhysics =
      const NeverScrollableScrollPhysics();

  late WidgetsBindingObserver appLifecycleObserver;

  @override
  void initState() {
    super.initState();

    /// 注册app生命周期
    appLifecycleObserver = AppLifecycleObserver(ref: ref);
    WidgetsBinding.instance.addObserver(appLifecycleObserver);

    /// apple pay
    initInAppPurchase();
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(appLifecycleObserver);
  }

  /// 初始化apple pay
  void initInAppPurchase() {
    // ref.read(appPurchaseProvider.notifier).initInAppPurchase();
    ref.read(zenAiAppPurchaseProvider.notifier).initInAppPurchase();
  }

  /// PageView
  Widget _buildPageView() {
    return PageView(
      controller: ref.watch(
        homeProvider.select((value) => value.pageController),
      ),
      physics: _scrollPhysics,
      children: const [
        ui_widgets.KeepAliveWrapper(child: GlideWidget()),
        ui_widgets.KeepAliveWrapper(child: SameStyleWidget()),
        ui_widgets.KeepAliveWrapper(child: PortfolioWidget()),
        ui_widgets.KeepAliveWrapper(child: GlideMeWidget()),
      ],
    );
  }

  /// bottom navigation bar
  Widget _buildBottomNavigationBar() {
    var selectIndex = ref.watch(
      homeProvider.select((value) => value.index),
    );
    final tabs = [
      TabItemData(0, "作品", glideHomeActive, glideHome),
      TabItemData(1, "同款", glideSameActive, glideSame),
      TabItemData(2, "作品", glideProductActive, glideProduct),
      TabItemData(3, "我的", glideMeActive, glideMe),
    ];
    var list = tabs
        .map(
          (e) => HomeTabBarItem(tabItemData: e, currentIndex: selectIndex),
        )
        .toList();

    Widget bgWidget = Row(
      children: tabs
          .map(
            (e) => Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 2.5),
                decoration: BoxDecoration(
                  color: const Color(0xFF18161A),
                  borderRadius: BorderRadius.circular(14),
                  border: Border.all(
                    color: const Color(0xFF3A3A3A),
                    width: 0.2,
                  ),
                ),
              ),
            ),
          )
          .toList(),
    );

    return DefaultTabController(
      length: tabs.length,
      child: Container(
        height: 61,
        padding: const EdgeInsets.fromLTRB(2.5, 5, 2.5, 5),
        decoration: BoxDecoration(
          color: const Color(0xFF29282B),
          borderRadius: BorderRadius.circular(18),
          border: Border.all(
            color: const Color(0xFF646464),
            width: 0.2,
          ),
        ),
        child: Stack(
          children: [
            bgWidget,
            TabBar(
              dividerHeight: 0,
              indicator: const HomeTabIndicator(),
              indicatorSize: TabBarIndicatorSize.label,
              indicatorWeight: 0,
              labelColor: const Color(0xFF141414),
              unselectedLabelColor: Colors.white,
              labelPadding: const EdgeInsets.symmetric(horizontal: 2.5),
              labelStyle: const TextStyle(fontSize: 14),
              unselectedLabelStyle: const TextStyle(fontSize: 14),
              tabs: list,
              onTap: (index) {
                if (ref.exists(homeProvider)) {
                  ref.read(homeProvider.notifier).jumpToPage(index);
                } else {
                  ref.watch(homeProvider.notifier).jumpToPage(index);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  // chat icon
  Widget _buildChatAction() {
    return Container(
      width: 61,
      height: 61,
      decoration: BoxDecoration(
        color: const Color(0xFF29282B),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: const Color(0xFF646464),
          width: 0.2,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(tabAiChat, width: 48),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return HomeFrameWidget(
      pageView: _buildPageView(),
      bottomTabBar: _buildBottomNavigationBar(),
      action: _buildChatAction(),
    );
  }
}
