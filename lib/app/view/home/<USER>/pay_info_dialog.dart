import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/member/member_provider.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/icon_address.dart';
import '../../../../config/constant.dart';
import '../../../navigation/router.dart';
import '../../../provider/in_app_purchase/app_purchase_provider.dart';

class ApplePayDialog {
  // 苹果支付产品显示
  static void showApplePayDialog(
    BuildContext context,
    WidgetRef ref,
    List<CustomAppleProduct> list,
  ) {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      context: context,
      builder: (_) {
        return ApplePayDialogWidget(list: list);
      },
    );
  }
}

class ApplePayDialogWidget extends ConsumerStatefulWidget {
  const ApplePayDialogWidget({
    super.key,
    required this.list,
  });

  final List<CustomAppleProduct> list;

  @override
  ApplePayDialogWidgetState createState() => ApplePayDialogWidgetState();
}

class ApplePayDialogWidgetState extends ConsumerState<ApplePayDialogWidget> {
  // late final AnimationController _shakeController = AnimationController(
  //   vsync: this,
  //   duration: const Duration(milliseconds: 500),
  // );

  @override
  void dispose() {
    // _shakeController.dispose();
    super.dispose();
  }

  // 会员权益项显示
  Widget _memberFeatItem(String icon, String name) {
    return Column(
      children: [
        Image.asset(
          icon,
          width: 52.w,
          height: 52.h,
        ),
        Text(
          name,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  // 会员信息项显示
  Widget _memberInfoItem(
    WidgetRef ref,
    CustomAppleProduct produce,
    CustomAppleProduct spec,
  ) {
    debugPrint("produce-----: ${produce.details.toString()}");
    var currentProduct = ref.watch(currentAppleProductProvider);
    bool isSelect = produce.productId == currentProduct?.productId;
    var rawPrice = produce.details?.rawPrice;
    var days = produce.package?.durationDayNum;
    var dailyPrices = "";
    if (rawPrice != null && days != null && days != 0) {
      if (produce.productId == ApplePayProduct.weeklyMember.productId) {
        dailyPrices = (rawPrice / days).toStringAsFixed(2);
      } else if (produce.productId == ApplePayProduct.yearMember.productId) {
        dailyPrices = (rawPrice / days).toStringAsFixed(2);
      } else if (produce.productId == ApplePayProduct.monthlyMember.productId) {
        dailyPrices = (rawPrice / days).toStringAsFixed(2);
      }
    }
    return SizedBox(
      // margin: EdgeInsets.symmetric(horizontal: 10.w),
      height: 160.h,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          InkWell(
            onTap: () {
              ref
                  .read(currentAppleProductProvider.notifier)
                  .setCurrentProduct(produce);
            },
            child: Container(
              height: 155.h,
              width: 110.w,
              decoration: BoxDecoration(
                color: isSelect
                    ? const Color(0xFFFEFAF0)
                    : const Color(0xFFFCFCFC),
                border: Border.all(
                  color: isSelect
                      ? const Color(0xFFFF0000)
                      : const Color(0xFFFFE1B2),
                  width: isSelect ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      SizedBox(
                        height: 18.h,
                      ),
                      Text(
                        "${produce.package?.memberName}",
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: isSelect
                              ? const Color(0xFFA05600)
                              : const Color(0xFF000000),
                        ),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      RichText(
                        text: TextSpan(
                          style: TextStyle(
                            color: isSelect
                                ? const Color(0xFFFF0000)
                                : const Color(0xFF000000),
                          ),
                          children: [
                            TextSpan(
                              text: "${produce.details?.currencySymbol}",
                              style: const TextStyle(fontSize: 18),
                            ),
                            const WidgetSpan(child: SizedBox(width: 4)),
                            TextSpan(
                              text: "${produce.details?.rawPrice}",
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      RichText(
                        text: TextSpan(
                          style: TextStyle(
                            color: isSelect
                                ? const Color(0xFFB77C31)
                                : const Color(0xFF8C8C8C),
                          ),
                          children: [
                            const TextSpan(
                              text: "原价",
                              style: TextStyle(fontSize: 10),
                            ),
                            const WidgetSpan(child: SizedBox(width: 3)),
                            TextSpan(
                              text: "¥${produce.package?.originalPrice}",
                              style: const TextStyle(
                                fontSize: 10,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      RichText(
                        text: TextSpan(
                          style: TextStyle(
                            color: isSelect
                                ? const Color(0xFFB77C31)
                                : const Color(0xFF8C8C8C),
                          ),
                          children: [
                            TextSpan(
                              text: "${produce.details?.currencySymbol}",
                              style: const TextStyle(fontSize: 12),
                            ),
                            const WidgetSpan(child: SizedBox(width: 3)),
                            TextSpan(
                              text: "$dailyPrices/天",
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 4.h),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFEEC5),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10.r),
                        bottomRight: Radius.circular(10.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "赠算力${produce.package?.powerNum}点",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFFA05600),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (spec.productId == produce.productId)
            Positioned(
              top: 0,
              left: 0,
              child: Container(
                padding: const EdgeInsets.fromLTRB(6, 3, 7, 3),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFFFE3C02),
                      Color(0xFFFE951D),
                    ],
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(6),
                    topRight: Radius.circular(6),
                    bottomRight: Radius.circular(6),
                  ),
                ),
                child: const Text(
                  "限时特惠",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var member = ref.watch(memberInfoProvider);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 26.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _memberFeatItem(fullFeaturesIcon, "解锁全部功能"),
            _memberFeatItem(multiTimbralIcon, "多种音色"),
            _memberFeatItem(memberLogoIcon, "会员标识"),
            _memberFeatItem(continuousUpdatesIcon, "持续更新"),
          ],
        ),
        SizedBox(height: 17.h),
        SizedBox(
          height: 170.h,
          width: MediaQuery.sizeOf(context).width,
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 13.w),
            scrollDirection: Axis.horizontal,
            child: Row(
              spacing: 10.w,
              mainAxisAlignment: MainAxisAlignment.start,
              children: widget.list
                  .map(
                    (e) => _memberInfoItem(ref, e, widget.list.first),
                  )
                  .toList(),
            ),
          ),
        ),
        SizedBox(height: 23.h),
        GradientButton(
          onPress: () {
            ref.read(appPurchaseProvider.notifier).purchaseById();
          },
          padding: const EdgeInsets.symmetric(vertical: 16),
          margin: const EdgeInsets.symmetric(horizontal: 18),
          gradient: const LinearGradient(
            colors: [Color(0xFF342A21), Color(0xFF483B30)],
          ),
          radius: 10.r,
          child: Text(
            member == null ? '立即开通' : '恢复订阅',
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFFFEDCAF),
            ),
          ),
        ),
        SizedBox(height: 10.h),
        RichText(
          text: TextSpan(
            style: const TextStyle(fontSize: 10, color: Colors.black),
            children: [
              const TextSpan(
                text: "开通会员代表同意",
                style: TextStyle(color: Color(0xFF818181)),
              ),
              TextSpan(
                text: "《用户协议》",
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    context.push(
                      "/$webPage",
                      extra: {
                        "title": "用户协议",
                        "url": Constant.userAgreementUrl
                      },
                    );
                  },
              ),
              const TextSpan(text: "和"),
              TextSpan(
                text: "《会员自动续费服务协议》",
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    context.push(
                      "/$webPage",
                      extra: {
                        "title": "会员自动续费服务协议",
                        "url": Constant.memberPolicyUrl
                      },
                    );
                  },
              ),
            ],
          ),
        ),
        SizedBox(
          height: MediaQuery.paddingOf(context).bottom == 0
              ? 16.h
              : MediaQuery.paddingOf(context).bottom + 12.h,
        ),
      ],
    );
  }
}
