import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ui_widgets/ui_widgets.dart';

class PortfolioWidget extends ConsumerStatefulWidget {
  const PortfolioWidget({super.key});

  @override
  PortfolioWidgetState createState() => PortfolioWidgetState();
}

class PortfolioWidgetState extends ConsumerState<PortfolioWidget> {
  Widget _buildHeader() {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: EdgeInsets.fromLTRB(
          16,
          MediaQuery.paddingOf(context).top + 11,
          0,
          11,
        ),
        child: const Text(
          "生成记录",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFFFFFFFF),
          ),
        ),
      ),
    );
  }

  Widget _buildItem() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 10),
      padding: const EdgeInsets.fromLTRB(12, 12, 10, 12),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            width: 64,
            height: 64,
            color: Colors.grey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SizedBox(
              height: 64,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "AI故事视频",
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFFFFFFFF),
                        ),
                      ),
                      Text(
                        "2025 / 11 / 02 09	:56",
                        style: TextStyle(
                          fontSize: 10,
                          color: Color(0xFF8A8D93),
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 14,
                            height: 14,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: const Color(0xFF30E6B8),
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: const Icon(
                              Icons.check,
                              color: Color(0xFF30E6B8),
                              size: 9,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Text(
                            "制作完成",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFFFFFFFF),
                            ),
                          ),
                        ],
                      ),
                      const Row(
                        children: [
                          Text(
                            "查看详情",
                            style: TextStyle(
                              fontSize: 10,
                              color: Color(0xFF8A8D93),
                            ),
                          ),
                          SizedBox(width: 3),
                          Icon(
                            Icons.arrow_forward_ios_rounded,
                            color: Color(0xFF8A8D93),
                            size: 10,
                          ),
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomListView(
      data: const [1, 2, 3, 4],
      sliverHeader: [
        _buildHeader(),
      ],
      renderItem: (context, index, o) {
        return _buildItem();
      },
    );
  }
}
