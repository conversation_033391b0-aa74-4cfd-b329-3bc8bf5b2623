import 'dart:convert';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/agent/conversation_provider.dart';
import 'package:text_generation_video/app/provider/home/<USER>';
import 'package:text_generation_video/utils/router_util.dart';
import '../../../repository/modals/main/main_banner.dart';

class MainBannerWidget extends ConsumerStatefulWidget {
  const MainBannerWidget({super.key});

  @override
  MainBannerWidgetState createState() => MainBannerWidgetState();
}

class MainBannerWidgetState extends ConsumerState<MainBannerWidget> {
  @override
  void initState() {
    super.initState();
  }

  // 解析配置并跳转
  void _jump(MainDataBanner banner) {
    try {
      var jumpType = banner.jumpType;
      var jumpParam = jsonDecode(banner.jumpParam!);
      var jump = jumpParam["jump"];
      switch (jumpType) {
        case 1:
          // 内部页面
          var url = jump["url"];
          var needLogin = jump["needLogin"] as bool;
          if (needLogin) {
            RouterUtil.checkLogin(context, call: () {
              context.push('/$url');
            });
          } else {
            context.push('/$url');
          }
          break;
        case 2:
          // 智能体
          var id = jump["id"];
          var title = jump["title"];
          var needLogin = jump["needLogin"] as bool;
          RouterUtil.checkLogin(context, call: () {
            ref
                .read(currentConversationProvider.notifier)
                .jumpConversation(context, id, title, needLogin);
          });
          break;
      }
    } catch (e) {
      debugPrint("$e");
    }
  }

  Widget _buildItem(MainDataBanner banner) {
    return InkWell(
      onTap: () {
        _jump(banner);
      },
      child: Image.network(banner.imgUrl ?? ""),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget child = SizedBox(height: MediaQuery.paddingOf(context).top);
    var bannerData = ref.watch(mainBannerDataProvider);
    var data = bannerData?.list;
    var aspectRatio = bannerData?.aspectRatio ?? 16 / 9;
    if (data != null && data.isNotEmpty) {
      child = CarouselSlider.builder(
        itemCount: data.length,
        itemBuilder: (context, index, view) {
          var item = data[index];
          return _buildItem(item);
        },
        options: CarouselOptions(
          aspectRatio: aspectRatio,
          autoPlay: data.length > 1,
          enableInfiniteScroll: data.length > 1,
          viewportFraction: 1,
        ),
      );
    }
    return SliverToBoxAdapter(
      child: child,
    );
  }
}
