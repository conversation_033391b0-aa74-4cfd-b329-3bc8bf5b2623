import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/view/home/<USER>/same_style_tab_widget.dart';
import 'package:waterfall_flow/waterfall_flow.dart';

import '../widget/glide_banner_widget.dart';

class SameStyleWidget extends ConsumerStatefulWidget {
  const SameStyleWidget({super.key});

  @override
  SameStyleWidgetState createState() => SameStyleWidgetState();
}

class SameStyleWidgetState extends ConsumerState<SameStyleWidget> {
  int crossAxisCount = 2;
  double crossAxisSpacing = 11.0;
  double mainAxisSpacing = 14.0;

  var list = List.generate(30, (e) => 200.0 + Random().nextInt(151)).toList();
  bool isLoading = false;
  bool hasMore = true;

  Future<void> _loadMore() async {
    if (isLoading || !hasMore) return;
    setState(() => isLoading = true);

    await Future.delayed(Duration(seconds: 2)); // 模拟网络请求

    setState(() {
      // 模拟数据加载
      if (list.length >= 50) {
        hasMore = false; // 没有更多数据
      } else {
        list.addAll(List.generate(10, (index) => 200.0 + Random().nextInt(151)));
      }
      isLoading = false;
    });
  }

  Widget _buildLastWidget() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMore();
    });
    return Container(
      margin: const EdgeInsets.only(top: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          isLoading
              ? const CircularProgressIndicator(strokeWidth: 2)
              : hasMore
                  ? const SizedBox.shrink()
                  : const Text(
                      "没有更多数据了",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                      ),
                    ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: SizedBox(height: MediaQuery.paddingOf(context).top),
        ),
        const GlideBannerWidget(),
        const SameStyleTabWidget(),
        SliverPadding(
          padding: EdgeInsets.fromLTRB(
            16,
            0,
            16,
            MediaQuery.paddingOf(context).bottom + 91,
          ),
          sliver: SliverWaterfallFlow(
            delegate: SliverChildBuilderDelegate(
              childCount: list.length + 1,
              (BuildContext c, int index) {
                if (index == list.length) {
                  return _buildLastWidget();
                }
                var height = list[index];
                return InkWell(
                  onTap: () {
                    context.push(
                      "/$sameStyleDetailPage",
                      extra: {
                        "tag": "same_style_$index"
                      },
                    );
                  },
                  child: Hero(
                    tag: "same_style_$index",
                    child: Container(
                      alignment: Alignment.center,
                      height: height,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.black),
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            "天空之城心灵语录",
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFFFFFFFF),
                            ),
                          ),
                          const SizedBox(height: 7),
                          Row(
                            children: [
                              Container(
                                width: 18,
                                height: 18,
                                color: Colors.grey,
                              ),
                              const SizedBox(width: 5),
                              const Text(
                                "李大力",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF8A8D93),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            gridDelegate: SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: crossAxisSpacing,
              mainAxisSpacing: mainAxisSpacing,
              collectGarbage: (List<int> garbages) {
                // print('collect garbage : $garbages');
              },
              viewportBuilder: (int firstIndex, int lastIndex) {
                // print('viewport : [$firstIndex,$lastIndex]');
              },
              lastChildLayoutTypeBuilder: (index) {
                return index == list.length
                    ? LastChildLayoutType.foot
                    : LastChildLayoutType.none;
              },
            ),
          ),
        ),
      ],
    );
  }

  double getRandomHeight() {
    return 200.0 + Random().nextInt(151);
  }
}
