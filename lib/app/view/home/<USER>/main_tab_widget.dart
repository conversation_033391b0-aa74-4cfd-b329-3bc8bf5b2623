import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/home/<USER>';
import 'package:text_generation_video/app/repository/modals/main/intelligent_group.dart';

class MainTabWidget extends ConsumerStatefulWidget {
  const MainTabWidget({super.key});

  @override
  MainTabWidgetState createState() => MainTabWidgetState();
}

class MainTabWidgetState extends ConsumerState<MainTabWidget> {
  int currentTab = 0;

  Widget _buildItemTab(
    BuildContext context,
    IntelligentGroup item,
    List<IntelligentGroup> tabs,
  ) {
    bool isActive = tabs[currentTab].id == item.id;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 12),
      margin: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        gradient: isActive
            ? const LinearGradient(
                colors: [Color(0xFFA7FF58), Color(0xFF00D572)])
            : const LinearGradient(
                colors: [Color(0xFF273337), Color(0xFF273337)]),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(item.groupName ?? ""),
    );
  }

  @override
  Widget build(BuildContext context) {
    var tabs = ref.watch(mainIntelligentGroupDataProvider);
    if (tabs == null || tabs.isEmpty) {
      return const SliverToBoxAdapter(child: SizedBox());
    }
    return DefaultTabController(
      length: tabs.length,
      child: SliverLayoutBuilder(
        builder: (context, constraints) {
          return SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.only(top: 14, bottom: 16),
              child: TabBar(
                isScrollable: true,
                tabAlignment: TabAlignment.start,
                dividerHeight: 0,
                indicator: const BoxDecoration(),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: const Color(0xFF141414),
                unselectedLabelColor: Colors.white,
                labelPadding: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(horizontal: 5),
                tabs: tabs
                    .map(
                      (e) => _buildItemTab(context, e, tabs),
                    )
                    .toList(),
                onTap: (index) {
                  debugPrint("index: $index");
                  setState(() {
                    currentTab = index;
                  });
                  var groupId = tabs[index].id;
                  ref
                      .read(mainIntelligentListDataProvider.notifier)
                      .loadIntelligent(groupId);
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
