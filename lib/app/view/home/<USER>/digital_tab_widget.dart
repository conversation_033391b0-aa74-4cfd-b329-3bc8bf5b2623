import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/app/provider/digital/digital_provider.dart';

class DigitalTabWidget extends ConsumerStatefulWidget {
  const DigitalTabWidget({super.key});

  @override
  DigitalTabWidgetState createState() => DigitalTabWidgetState();
}

class DigitalTabWidgetState extends ConsumerState<DigitalTabWidget> {
  int currentTab = 0;

  // 筛选
  void _filter(
    BuildContext targetContext,
    WidgetRef ref,
    List<DigitalTab> tabs,
    SelectorFilter filterData,
  ) {
    var tabData = tabs[currentTab];
    if (tabData.childList == null || tabData.childList!.isEmpty) return;
    SmartDialog.showAttach(
      keepSingle: true,
      targetContext: targetContext,
      builder: (context) {
        return Container(
          constraints: const BoxConstraints(maxHeight: 144),
          decoration: const BoxDecoration(
            color: Color(0xFF141414),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            padding: const EdgeInsets.only(top: 12),
            itemBuilder: (BuildContext context, int index) {
              return _buildChildItem(
                  ref, tabData.childList![index], filterData);
            },
            itemCount: tabData.childList!.length,
            separatorBuilder: (BuildContext context, int index) {
              return const Divider(
                height: 0.8,
                color: Color(0x13FFFFFF),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildChildItem(
    WidgetRef ref,
    DigitalChildTab childTab,
    SelectorFilter filterData,
  ) {
    bool isActive = filterData.gender?.id == childTab.id ||
        filterData.person?.id == childTab.id ||
        filterData.audio?.id == childTab.id;
    return InkWell(
      onTap: () {
        ref
            .read(publicDigitalFilterSelectProvider.notifier)
            .setSelect(childTab);
        SmartDialog.dismiss();
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(10, 6, 0, 6),
        child: Row(
          children: [
            Container(
              width: 3,
              height: 15,
              decoration: BoxDecoration(
                gradient: isActive
                    ? const LinearGradient(
                        colors: [
                          Color(0xFF00D672),
                          Color(0xFF53FF8A),
                        ],
                      )
                    : null,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 6),
            Text(
              childTab.tabName,
              style: const TextStyle(
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabItem(List<DigitalTab> tabs, DigitalTab item) {
    bool isActive = tabs[currentTab].id == item.id;
    Widget triangleIcon = const SizedBox();
    if (item.childList != null && item.childList!.isNotEmpty) {
      triangleIcon = const Padding(
        padding: EdgeInsets.only(left: 7),
        child: TriangleIcon(size: Size(8, 5)),
      );
    }
    Widget child = Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 12),
      margin: const EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        gradient: isActive
            ? const LinearGradient(
                colors: [Color(0xFFA7FF58), Color(0xFF00D572)])
            : const LinearGradient(
                colors: [Color(0xFF273337), Color(0xFF273337)]),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(item.tabName),
          triangleIcon,
        ],
      ),
    );
    return child;
  }

  @override
  Widget build(BuildContext context) {
    var filterData = ref.watch(publicDigitalFilterSelectProvider);
    var tabs = ref.watch(publicDigitalFilterListProvider);
    var list = tabs.map((e) => _buildTabItem(tabs, e)).toList();
    return DefaultTabController(
      length: tabs.length,
      child: SliverToBoxAdapter(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Container(
              margin: const EdgeInsets.only(top: 14, bottom: 16),
              child: TabBar(
                isScrollable: true,
                tabAlignment: TabAlignment.start,
                dividerHeight: 0,
                indicator: const BoxDecoration(),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: const Color(0xFF141414),
                unselectedLabelColor: Colors.white,
                labelPadding: EdgeInsets.zero,
                labelStyle: const TextStyle(fontSize: 14),
                unselectedLabelStyle: const TextStyle(fontSize: 14),
                padding: const EdgeInsets.symmetric(horizontal: 5),
                tabs: list,
                onTap: (index) {
                  debugPrint("index: $index");
                  if (currentTab != index) {
                    setState(() {
                      currentTab = index;
                    });
                  }
                  if (index == 0) {
                    ref
                        .read(publicDigitalFilterSelectProvider.notifier)
                        .setSelectAll();
                  } else {
                    _filter(context, ref, tabs, filterData);
                  }
                },
              ),
            );
          },
        ),
      ),
    );
  }
}

// 三角形形状代替icon
class TriangleIcon extends StatelessWidget {
  final Size size;

  const TriangleIcon({
    super.key,
    this.size = Size.zero,
  });

  @override
  Widget build(BuildContext context) {
    final color = IconTheme.of(context).color ?? Colors.black;
    return SizedBox(
      width: size.width,
      height: size.height,
      child: CustomPaint(
        painter: _TrianglePainter(color),
      ),
    );
  }
}

class _TrianglePainter extends CustomPainter {
  final Color color;

  _TrianglePainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = color;

    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width / 2, size.height)
      ..lineTo(size.width, 0)
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
