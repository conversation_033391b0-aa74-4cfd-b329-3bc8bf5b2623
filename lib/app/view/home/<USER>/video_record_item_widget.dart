import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/home/<USER>';
import 'package:text_generation_video/app/repository/modals/digital/customize_video.dart';
import 'package:text_generation_video/app/view/home/<USER>/operate_video_dialog.dart';
import 'package:text_generation_video/config/icon_address.dart';

import '../../../../utils/platform_util.dart';
import '../../../provider/creation/creation_provider.dart';
import '../../../provider/digital/digital_video_provider.dart';
import '../../../provider/in_app_purchase/app_purchase_provider.dart';
import '../dialog/pay_info_dialog.dart';

class VideoRecordItemWidget extends ConsumerWidget {
  const VideoRecordItemWidget({
    super.key,
    required this.customizeVideo,
    required this.width,
  });

  final CustomizeVideo customizeVideo;
  final double width;

  void _buyPackage(BuildContext context, WidgetRef ref) async {
    if (PlatformUtils.isIOS) {
      var list = await ref.read(appPurchaseProvider.notifier).loadAllProducts();
      if (list != null && list.isNotEmpty && context.mounted) {
        ApplePayDialog.showApplePayDialog(
          context,
          ref,
          list,
        );
      }
    }
  }

  void _moreOperate(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(22),
              topRight: Radius.circular(22),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (customizeVideo.state == 1)
                InkWell(
                  onTap: () {
                    context.pop();
                    ref
                        .read(creationVideoProvider.notifier)
                        .downloadVideo(customizeVideo.videoUrl ?? "");
                  },
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 15),
                        child: Text(
                          "下载",
                          style:
                              TextStyle(fontSize: 16, color: Color(0xFF141414)),
                        ),
                      ),
                    ],
                  ),
                ),
              if (customizeVideo.state == 1)
                const Divider(height: 1, color: Color(0xFFF3F3F3)),
              InkWell(
                onTap: () async {
                  context.pop();
                  ref.read(digitalMakeVideoProvider.notifier).updateVideoAlias(
                        customizeVideo.id,
                        customizeVideo.videoAlias,
                      );
                },
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 15),
                      child: Text(
                        "重命名",
                        style:
                            TextStyle(fontSize: 16, color: Color(0xFF141414)),
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1, color: Color(0xFFF3F3F3)),
              InkWell(
                onTap: () {
                  context.pop();
                  ref
                      .read(digitalMakeVideoProvider.notifier)
                      .deleteVideo(customizeVideo.id);
                },
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 15),
                      child: Text(
                        "删除",
                        style:
                            TextStyle(fontSize: 16, color: Color(0xFF141414)),
                      ),
                    ),
                  ],
                ),
              ),
              Container(height: 5, color: const Color(0xFFF3F3F3)),
              InkWell(
                onTap: () {
                  context.pop();
                },
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 15),
                      child: Text(
                        "取消",
                        style:
                            TextStyle(fontSize: 16, color: Color(0xFF141414)),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: MediaQuery.paddingOf(context).bottom),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (customizeVideo.id == -100) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                ref.read(homeProvider.notifier).jumpToPage(0);
              },
              child: Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF224E4C),
                      Color(0xFF1F393A),
                      Color(0xFF1D272B)
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add, color: Color(0xFFFFFFFF), size: 14),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 6),
          const Text(
            "新建数字人视频",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 11,
              color: Colors.white,
            ),
          ),
        ],
      );
    }

    // state
    Widget stateIcon = const SizedBox();
    Widget stateDesc = const SizedBox();
    Widget failReason = const SizedBox();
    Widget success = const SizedBox();
    Widget backBg = const SizedBox();
    if (customizeVideo.state == 0) {
      // 处理中
      backBg = Image.asset(
        videoBgFail,
        width: width,
        height: double.infinity,
        fit: BoxFit.fill,
      );
      stateIcon = Padding(
        padding: const EdgeInsets.only(bottom: 6),
        child: Image.asset(videoProgressIcon, width: 16, height: 16),
      );
      stateDesc = const Text(
        "正在制作中",
        style: TextStyle(
          fontSize: 10,
          color: Colors.white,
        ),
      );
    } else if (customizeVideo.state == 2) {
      // 失败
      backBg = Image.asset(
        videoBgFail,
        width: width,
        height: double.infinity,
        fit: BoxFit.fill,
      );
      stateIcon = Padding(
        padding: const EdgeInsets.only(bottom: 6),
        child: Image.asset(videoFailIcon, width: 16, height: 16),
      );
      stateDesc = const Text(
        "训练失败",
        style: TextStyle(
          fontSize: 10,
          color: Colors.white,
        ),
      );
      failReason = InkWell(
        onTap: () async {
          var result = await RemasteredDialog.remasteredVideo(
              customizeVideo.failMsg ?? "");
          if (result == true) {
            ref.read(digitalMakeVideoProvider.notifier).reCreateVideo(
              () {
                context.push("/$makeVideoDigitalProcessPage");
              },
              () {
                _buyPackage(context, ref);
              },
              customizeVideo,
            );
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 5),
          margin: const EdgeInsets.only(top: 5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            border: Border.all(color: Colors.white, width: 0.6),
          ),
          child: const Text(
            "查看原因",
            style: TextStyle(
              fontSize: 10,
              color: Colors.white,
            ),
          ),
        ),
      );
    } else if (customizeVideo.state == 1) {
      backBg = ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: CachedNetworkImage(
          imageUrl: customizeVideo.previewUrl ?? "",
          width: width,
          fit: BoxFit.cover,
          errorWidget: (context, url, o) {
            return Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF224E4C),
                    Color(0xFF1F393A),
                    Color(0xFF1D272B)
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
            );
          },
        ),
      );
      success = InkWell(
        onTap: () {
          context.push(
            '/$digitalPreviewPage',
            extra: customizeVideo.videoUrl,
          );
        },
        child: Image.asset(digitalPreviewIcon, width: 29, height: 29),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: SizedBox(
            width: width,
            child: Stack(
              alignment: Alignment.center,
              children: [
                backBg,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        stateIcon,
                        const SizedBox(height: 6),
                        stateDesc,
                        failReason,
                        success,
                        const SizedBox(height: 10),
                      ],
                    ),
                  ],
                ),
                Positioned(
                  bottom: 3,
                  right: 3,
                  child: InkWell(
                    onTap: () {
                      _moreOperate(context, ref);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 5,
                      ),
                      decoration: BoxDecoration(
                        color: customizeVideo.state == 1
                            ? const Color(0x73000000)
                            : const Color(0x00000000),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: Image.asset(videoMoreIcon, width: 13, height: 3),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 6),
        Text(
          customizeVideo.videoAlias ?? "",
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontSize: 11,
            color: Colors.white,
          ),
        ),
      ],
    );
  }
}
