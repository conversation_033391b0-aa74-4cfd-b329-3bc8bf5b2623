import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ui_widgets/ui_widgets.dart' as ui_widgets;

final List<String> tabs = ["数字人视频"];

class ProductionTabWidget extends ConsumerStatefulWidget {
  const ProductionTabWidget({super.key});

  @override
  ProductionTabWidgetState createState() => ProductionTabWidgetState();
}

class ProductionTabWidgetState extends ConsumerState<ProductionTabWidget> {
  int currentTab = 0;

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      child: SliverToBoxAdapter(
        child: Container(
          margin: const EdgeInsets.only(top: 40, bottom: 16),
          child: TabBar(
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            dividerHeight: 0.8,
            dividerColor: const Color(0x13FFFFFF),
            indicator: const ui_widgets.RectTabIndicator(
              isPin: true,
              offset: 0,
              gradient: LinearGradient(
                colors: [Color(0xFF00CE6E), Color(0xFF00CE6E)],
              ),
            ),
            // indicatorColor: const Color(0xFF00CE6E),
            indicatorSize: TabBarIndicatorSize.label,
            labelColor: const Color(0xFFFFFFFF),
            unselectedLabelColor: const Color(0xFF949599),
            tabs: tabs.map((e) => Tab(text: e)).toList(),
            onTap: (index) {},
          ),
        ),
      ),
    );
  }
}
