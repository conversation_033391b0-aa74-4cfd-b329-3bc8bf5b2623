import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class GlideBannerWidget extends ConsumerStatefulWidget {
  const GlideBannerWidget({super.key});

  @override
  GlideBannerWidgetState createState() => GlideBannerWidgetState();
}

class GlideBannerWidgetState extends ConsumerState<GlideBannerWidget> {
  @override
  void initState() {
    super.initState();
  }

  // 解析配置并跳转
  // void _jump(MainDataBanner banner) {
  //   try {
  //     var jumpType = banner.jumpType;
  //     var jumpParam = jsonDecode(banner.jumpParam!);
  //     var jump = jumpParam["jump"];
  //     switch (jumpType) {
  //       case 1:
  //         // 内部页面
  //         var url = jump["url"];
  //         var needLogin = jump["needLogin"] as bool;
  //         if (needLogin) {
  //           RouterUtil.checkLogin(context, call: () {
  //             context.push('/$url');
  //           });
  //         } else {
  //           context.push('/$url');
  //         }
  //         break;
  //       case 2:
  //         // 智能体
  //         var id = jump["id"];
  //         var title = jump["title"];
  //         var needLogin = jump["needLogin"] as bool;
  //         RouterUtil.checkLogin(context, call: () {
  //           ref
  //               .read(currentConversationProvider.notifier)
  //               .jumpConversation(context, id, title, needLogin);
  //         });
  //         break;
  //     }
  //   } catch (e) {
  //     debugPrint("$e");
  //   }
  // }

  Widget _buildItem() {
    return InkWell(
      onTap: () {
        // _jump(banner);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Container(
          color: Colors.amber,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: CarouselSlider.builder(
        itemCount: 1,
        itemBuilder: (context, index, view) {
          return _buildItem();
        },
        options: CarouselOptions(
          aspectRatio: 3.42 / 1,
          autoPlay: false,
          enableInfiniteScroll: false,
          viewportFraction: 1,
        ),
      ),
    );

    // Widget child = const SizedBox();
    // var bannerData = ref.watch(mainBannerDataProvider);
    // var data = bannerData?.list;
    // var aspectRatio = bannerData?.aspectRatio ?? (3.42 / 1);
    // if (data != null && data.isNotEmpty) {
    //   child = CarouselSlider.builder(
    //     itemCount: data.length,
    //     itemBuilder: (context, index, view) {
    //       var item = data[index];
    //       return _buildItem(item);
    //     },
    //     options: CarouselOptions(
    //       aspectRatio: aspectRatio,
    //       autoPlay: data.length > 1,
    //       enableInfiniteScroll: data.length > 1,
    //       viewportFraction: 1,
    //     ),
    //   );
    // }
    // return SliverToBoxAdapter(
    //   child: child,
    // );
  }
}
