import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting.dialog
/// @ClassName: delete_video_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/11 11:44
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/11 11:44
/// @UpdateRemark: 更新说明
class DeleteVideoDialog {
  static Future<bool?> confirmDelete() async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "delete_video_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 46.h, bottom: 42.h),
                    child: Text(
                      "删除后不可恢复，是否确认？",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                  Divider(
                    indent: 5.w,
                    endIndent: 5.w,
                    color: const Color(0xFFE6E6E6),
                    height: 1,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "delete_video_dialog",
                              result: false,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "取消",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: 24.h,
                        color: const Color(0xFFE6E6E6),
                      ),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "delete_video_dialog",
                              result: true,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Text(
                              "确认删除",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFF4C4C),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

// 重新制作视频弹窗
class RemasteredDialog {
  static Future<bool?> remasteredVideo(String failText) async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              padding: const EdgeInsets.fromLTRB(22, 22, 22, 15),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 27),
                    child: Text(
                      "温馨提示",
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: const Color(0xFF000000),
                      ),
                    ),
                  ),
                  Text(
                    failText,
                    style:
                        const TextStyle(fontSize: 14, color: Color(0xFF505052)),
                  ),
                  const SizedBox(height: 27),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(result: false);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FD),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 11),
                            child: Text(
                              "关闭",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF010101),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(result: true);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xFF00D773),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 11),
                            child: Text(
                              "重新制作",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

// 修改视频名称弹窗
class UpdateVideoAliasDialog {
  static Future<String?> updateVideo(String? oldName) async {
    TextEditingController controller = TextEditingController()
      ..text = oldName ?? "";
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              padding: const EdgeInsets.fromLTRB(15, 22, 15, 15),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Text(
                      "请填写视频名称",
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: const Color(0xFF000000),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF0F1F3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextField(
                      decoration: const InputDecoration(
                        hintText: "请输入视频名称",
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF505052),
                        ),
                      ),
                      maxLines: 1,
                      maxLength: 32,
                      buildCounter: (
                        BuildContext context, {
                        required int currentLength,
                        required bool isFocused,
                        required int? maxLength,
                      }) {
                        return null; // ✅ 返回 null 即可隐藏计数器
                      },
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                      ),
                      controller: controller,
                    ),
                  ),
                  const SizedBox(height: 23),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(result: null);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FD),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 11),
                            child: Text(
                              "关闭",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF010101),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            if (controller.text.isNotEmpty) {
                              SmartDialog.dismiss(result: controller.text);
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xFF00D773),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 11),
                            child: Text(
                              "确定提交",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

// 确认删除视频弹窗
class DeleteCustomizeVideoDialog {
  static Future<bool?> deleteVideo() async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              padding: const EdgeInsets.fromLTRB(15, 22, 15, 15),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 30),
                    child: Text(
                      "确认删除",
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: const Color(0xFF000000),
                      ),
                    ),
                  ),
                  const Text(
                    "您确认删除该作品记录吗？",
                    style: TextStyle(fontSize: 14, color: Color(0xFF505052)),
                  ),
                  const SizedBox(height: 34),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(result: false);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FD),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 11),
                            child: Text(
                              "关闭",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFF010101),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(result: true);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xFF00D773),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 11),
                            child: Text(
                              "确认删除",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
