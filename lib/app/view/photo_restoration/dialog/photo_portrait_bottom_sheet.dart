import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:text_generation_video/config/icon_address.dart';

/// 图片修复底部弹窗
class PhotoPortraitUploadBottomSheet extends StatelessWidget {
  const PhotoPortraitUploadBottomSheet({
    this.onConfirm,
    this.onCancel,
    this.child,
  });

  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          // Container(
          //   margin: const EdgeInsets.only(top: 12),
          //   width: 40,
          //   height: 4,
          //   decoration: BoxDecoration(
          //     color: const Color(0xFF565656),
          //     borderRadius: BorderRadius.circular(2),
          //   ),
          // ),

          Padding(
            padding: const EdgeInsets.fromLTRB(24, 8, 24, 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context),
                const SizedBox(height: 16),
                child ?? const SizedBox(),
                _buildControls(),
                SizedBox(height: MediaQuery.of(context).padding.bottom),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Row(
          children: [
            const Text(
              "请选择一张面部清晰的照片",
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 2),
            Image.asset(
              recoedAudioIcon,
              width: 30,
            )
          ],
        ),
        const Spacer(),
        IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: const Color(0xFF3E3C3E),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF858585),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 18,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildControls() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: onConfirm,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF30E6B8),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: const Text(
              "选择图片",
              style: TextStyle(
                color: Color(0xFF18161A),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class PhotoPortraitSelectImgBottomSheet extends StatelessWidget {
  const PhotoPortraitSelectImgBottomSheet({
    super.key,
    this.onConfirm,
    this.onDelete,
    this.imgUrl,
  });

  final VoidCallback? onConfirm;
  final VoidCallback? onDelete;
  final String? imgUrl;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 10,
          right: 10,
          child: IconButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            icon: Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: const Color(0xFF3E3C3E),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF858585),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ),
        Container(
          height: 500,
          padding: const EdgeInsets.fromLTRB(24, 8, 24, 24),
          child: Column(
            children: [
              Expanded(
                child: Container(
                  transform: Matrix4.translationValues(0, -50, 0),
                  width: 300,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: CachedNetworkImage(
                      imageUrl: imgUrl ?? "",
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey,
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey,
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.white54,
                            size: 32,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              _buildButtons(context),
              SizedBox(height: 10),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButtons(
    BuildContext context,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 80,
          child: _buildButton(
            Image.asset(
              photoPortraitDeleteIcon,
              width: 18,
            ),
            const Color(0xff234C44),
            () {
              onDelete?.call();
              Navigator.pop(context);
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildButton(
            const Text(
              "使用照片",
              style: TextStyle(
                fontSize: 14,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Color(0xFF30E6B8),
            () {
              onConfirm?.call();
              Navigator.pop(context);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildButton(Widget chile, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 140,
        height: 56,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(16),
        ),
        alignment: Alignment.center,
        child: chile,
      ),
    );
  }
}
