import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_modification.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../widgets/video_case/photo_compare_widget.dart';

class ModificationDemoDialog {
  static void showModificationDemoDialog(PhotoModification modification) {
    SmartDialog.show(
      builder: (context) {
        return ModificationDemoDialogWidget(modification: modification);
      },
    );
  }
}

class ModificationDemoDialogWidget extends ConsumerWidget {
  const ModificationDemoDialogWidget({
    super.key,
    required this.modification,
  });

  final PhotoModification modification;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 269,
      height: 361,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: St<PERSON>(
        children: [
          OldPhotoCompare(
            before: CachedNetworkImage(
              imageUrl: modification.originalImgUrl ?? "",
              fit: BoxFit.cover,
            ),
            after: CachedNetworkImage(
              imageUrl: modification.aiImgUrl ?? "",
              fit: BoxFit.cover,
            ),
            borderRadius: BorderRadius.circular(16),
            initialPosition: 0.5, // 0.0=全显示before, 1.0=全显示after
          ),
          Positioned(
            top: 10,
            right: 10,
            child: InkWell(
              onTap: () {
                SmartDialog.dismiss();
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 18),
              ),
            ),
          ),
          Positioned(
            bottom: 11,
            left: 0,
            right: 0,
            child: GradientButton(
              onPress: () {
                SmartDialog.dismiss();
                ref
                    .read(modificationEditTextProvider.notifier)
                    .setText(modification.prompt);
              },
              padding: const EdgeInsets.symmetric(vertical: 14),
              margin: const EdgeInsets.symmetric(horizontal: 10),
              radius: 23,
              shadow: false,
              gradient: const LinearGradient(
                colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
              ),
              child: const Text(
                "创作同款",
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF18161A),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
