import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../provider/photo_repair/photo_repair_provider.dart';
import '../../widgets/appbar/leading.dart';
import '../../widgets/consumption/consumption_display_widget.dart';
import 'widget/matting_demo_widget.dart';

class PhotoMattingPage extends ConsumerWidget {
  const PhotoMattingPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "AI抠图",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {},
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 21),
            const MatingDemoWidget(),
            const SizedBox(height: 25),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: ConsumptionDisplayWidget(consumption: 20),
            ),
            GradientButton(
              onPress: () {
                ref
                    .read(photoRepairActionProvider.notifier)
                    .saliencySegSubmitTask();
              },
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.symmetric(vertical: 15),
              radius: 16,
              shadow: false,
              gradient: const LinearGradient(
                colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
              ),
              child: const Text(
                "立即抠图",
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF18161A),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
