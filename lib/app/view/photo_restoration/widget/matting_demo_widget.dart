import 'package:flutter/material.dart';
import 'package:text_generation_video/config/icon_address.dart';

class MatingDemoWidget extends StatefulWidget {
  const MatingDemoWidget({super.key});

  @override
  MatingDemoWidgetState createState() => MatingDemoWidgetState();
}

class MatingDemoWidgetState extends State<MatingDemoWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.ease,
    ));

    _controller.addStatusListener(_statusListener);

    Future.delayed(const Duration(milliseconds: 800), () {
      _controller.forward(); // 启动
    });
  }

  void _statusListener(status) async {
    if (status == AnimationStatus.dismissed) {
      // 一次完整循环结束 (0→1→0)
      await Future.delayed(const Duration(milliseconds: 800)); // 停顿 0.5s
      if (mounted) {
        _controller.forward(); // 重新开始
      }
    } else if (status == AnimationStatus.completed) {
      if (mounted) {
        // 到达 1 后反向
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.removeStatusListener(_statusListener);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final w = MediaQuery.sizeOf(context).width;
    final h = 544 * w / 375.27;
    final dw = (278.68 / 375.27) * w;
    return SizedBox(
      width: w,
      height: h,
      child: Stack(
        children: [
          Image.asset(mattingTransparentBg, width: w),
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return RightClip(
                clipXFraction: _animation.value,
                child: Image.asset(mattingDemoBg, width: w),
              );
            },
          ),
          Positioned(
            bottom: 34 / 544 * h,
            left: 35 / 375.27 * w,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                if (_animation.value >= 0.5) {
                  var aValue = 1.0 + 0.2 * (_animation.value - 0.5);
                  return Transform.scale(
                    scale: aValue,
                    child: Image.asset(mattingDemo, width: dw),
                  );
                }
                return Image.asset(mattingDemo, width: dw);
              },
            ),
          ),
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Positioned(
                left: (_animation.value * w) - (4 / 2),
                child: IgnorePointer(
                  child: Container(
                    height: h,
                    width: 4,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black54,
                          offset: Offset(2.0, 0.0),
                          blurRadius: 12.0,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

/// 左侧裁剪器：显示 [0, clipX] 的内容
class RightClip extends StatelessWidget {
  const RightClip(
      {super.key, required this.clipXFraction, required this.child});

  final double clipXFraction;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, c) {
      final w = c.maxWidth;
      return ClipPath(
        clipper: _LeftClipper(clipX: (clipXFraction * w).clamp(0, w)),
        child: child,
      );
    });
  }
}

class _LeftClipper extends CustomClipper<Path> {
  _LeftClipper({required this.clipX});

  final double clipX;

  @override
  Path getClip(Size size) {
    final x = size.width - clipX.clamp(0.0, size.width);
    final path = Path()
      ..addRect(Rect.fromLTWH(clipX.clamp(0.0, size.width), 0, x, size.height))
      ..close();
    return path;
  }

  @override
  bool shouldReclip(covariant _LeftClipper oldClipper) =>
      oldClipper.clipX != clipX;
}
