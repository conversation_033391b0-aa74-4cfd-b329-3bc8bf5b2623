import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/icon_address.dart';

class ModificationUploadWidget extends ConsumerWidget {
  const ModificationUploadWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // var modificationList = ref.watch(photoModificationListProvider);
    var currentModification = ref.watch(photoModificationCurrentProvider);

    // if (modificationList != null && modificationList.isNotEmpty) {
    //   // 有操作记录
    //   return RecordWidget(modificationList: modificationList);
    // } else {
    //   // 无操作记录
    //   return NoRecordWidget(modification: currentModification);
    // }
    return NoRecordWidget(modification: currentModification);
  }
}

// 有记录
class RecordWidget extends ConsumerStatefulWidget {
  const RecordWidget({super.key, required this.modificationList});

  final List<Modification> modificationList;

  @override
  RecordWidgetState createState() => RecordWidgetState();
}

class RecordWidgetState extends ConsumerState<RecordWidget> {
  late Modification currentMod;

  @override
  void initState() {
    super.initState();
    currentMod = widget.modificationList.last;
    debugPrint("RecordWidgetState: ${currentMod.remoteUrl}");
  }

  @override
  void didUpdateWidget(covariant RecordWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.modificationList != widget.modificationList) {
      setState(() {
        currentMod = widget.modificationList.last;
      });
    }
  }

  // currentMod是否是modificationList中的第一个，如果是则没有后退，返回false
  bool hasUndo() =>
      currentMod.remoteUrl != widget.modificationList.first.remoteUrl;

  // currentMod是否是modificationList中的最后一个，如果是则没有前进，返回false
  bool hasRedo() =>
      currentMod.remoteUrl != widget.modificationList.last.remoteUrl;

  // 不是第一个，点击就跳转上一个
  void undo() {
    if (hasUndo()) {
      var index = widget.modificationList
          .indexWhere((e) => e.remoteUrl == currentMod.remoteUrl);
      var previous = widget.modificationList[index - 1];
      setState(() {
        currentMod = previous;
      });
    }
  }

  // 不是最后一个，点击就跳转下一个
  void redo() {
    if (hasRedo()) {
      var index = widget.modificationList
          .indexWhere((e) => e.remoteUrl == currentMod.remoteUrl);
      var next = widget.modificationList[index + 1];
      setState(() {
        currentMod = next;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, c) {
        debugPrint("RecordWidget-maxW: ${c.maxWidth}");
        debugPrint("RecordWidget-maxH: ${c.maxHeight}");
        debugPrint("RecordWidget-maxH: ${currentMod.remoteUrl}");
        return Stack(
          children: [
            Container(
              width: c.maxWidth,
              height: c.maxHeight,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(modificationBg),
                  fit: BoxFit.fill,
                ),
              ),
              child: CachedNetworkImage(imageUrl: currentMod.remoteUrl ?? ""),
            ),
            if (widget.modificationList.length > 1)
              Positioned(
                left: 10,
                bottom: 16,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Row(
                    children: [
                      InkWell(
                        onTap: () {
                          undo();
                        },
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(11, 7, 6, 7),
                          child: Image.asset(
                            undoIcon,
                            width: 13.79,
                            color: hasUndo() ? Colors.white : Colors.grey,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          redo();
                        },
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(6, 7, 11, 7),
                          child: Image.asset(
                            redoIcon,
                            width: 13.79,
                            color: hasRedo() ? Colors.white : Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            Positioned(
              top: 10,
              right: 16,
              child: InkWell(
                onTap: () {
                  ref.read(photoModificationListProvider.notifier).clean();
                  ref.read(photoModificationCurrentProvider.notifier).clean();
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.close, color: Colors.white, size: 18),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

// 无记录
class NoRecordWidget extends ConsumerWidget {
  const NoRecordWidget({super.key, required this.modification});

  final Modification modification;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Widget child = const SizedBox();
    if (modification.state == 0) {
      // 默认
      child = Center(
        child: InkWell(
          onTap: () {
            RouterUtil.checkLogin(
              context,
              call: () {
                ref.read(photoModificationCurrentProvider.notifier).selectImg();
              },
            );
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(modificationSelectIcon, width: 26),
              const SizedBox(height: 9),
              const Text(
                "添加图片",
                style: TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
              )
            ],
          ),
        ),
      );
    } else if (modification.state == 1) {
      // 上传中
      child = const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Uploading(),
            SizedBox(height: 12),
            Text(
              "上传中",
              style: TextStyle(fontSize: 12, color: Color(0xFFFFFFFF)),
            )
          ],
        ),
      );
    } else if (modification.state == 2) {
      // 上传成功 CachedNetworkImage(imageUrl: modification.remoteUrl ?? "");
      child = Image.file(File(modification.localUrl ?? ""));
    } else if (modification.state == 3) {
      // 上传失败
      child = Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GradientButton(
                onPress: () {
                  RouterUtil.checkLogin(
                    context,
                    call: () {
                      ref
                          .read(photoModificationCurrentProvider.notifier)
                          .selectImg();
                    },
                  );
                },
                shadow: false,
                padding:
                    const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
                gradient: const LinearGradient(
                  colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                ),
                child: const Text(
                  "重新上传",
                  style: TextStyle(
                    fontSize: 10,
                    color: Color(0xFF18161A),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }

    return LayoutBuilder(
      builder: (context, c) {
        debugPrint("NoRecordWidget-maxW: ${c.maxWidth}");
        debugPrint("NoRecordWidget-maxH: ${c.maxHeight}");
        return Stack(
          children: [
            Container(
              width: c.maxWidth,
              height: c.maxHeight,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(modificationBg),
                  fit: BoxFit.fill,
                ),
              ),
              child: child,
            ),
            if (modification.state == 2)
              Positioned(
                top: 10,
                right: 16,
                child: InkWell(
                  onTap: () {
                    ref.read(photoModificationCurrentProvider.notifier).clean();
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child:
                        const Icon(Icons.close, color: Colors.white, size: 18),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

// 图片上传动画
class Uploading extends StatefulWidget {
  const Uploading({super.key});

  @override
  UploadingState createState() => UploadingState();
}

class UploadingState extends State<Uploading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600), // 一圈时间
      vsync: this,
    )..repeat(); // 无限循环旋转
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: Image.asset(uploadLoading, width: 38),
    );
  }
}
