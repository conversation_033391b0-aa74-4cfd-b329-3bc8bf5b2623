import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_case_provider.dart';

import '../dialog/modification_demo_dialog.dart';

class ModificationDemoWidget extends ConsumerWidget {
  const ModificationDemoWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchModificationCaseProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return const SizedBox();
        }
        return SizedBox(
          height: 66,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemBuilder: (context, index) {
              var item = data[index];
              return InkWell(
                onTap: () {
                  ModificationDemoDialog.showModificationDemoDialog(item);
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(18),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CachedNetworkImage(
                        imageUrl: item.aiImgUrl ?? "",
                        width: 66,
                        height: 66,
                        fit: BoxFit.cover,
                      ),
                      Container(
                        width: 66,
                        height: 66,
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Color(0x00000000),
                              Color(0x00000000),
                              Color(0xEE18161A),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 6,
                        child: Text(
                          "${item.caseTitle}",
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return const SizedBox(width: 6);
            },
            itemCount: data.length,
          ),
        );
      },
      error: (o, s) {
        return const SizedBox();
      },
      loading: () {
        return const SizedBox();
      },
    );
  }
}
