import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_portrait_provide.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';

import 'package:text_generation_video/app/widgets/appbar/leading.dart';

class PhotoPortraitPage extends ConsumerWidget {
  const PhotoPortraitPage({super.key});
  
  Widget _buildHeader(List<PhotoPortraitBanner> bannerData) {
    return SizedBox(
      height: 80,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: bannerData.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final banner = bannerData[index];
          return _buildBannerItem(context,banner);
        },
      ),
    );
  }

  Widget _buildBannerItem(BuildContext context,PhotoPortraitBanner banner) {
    return GestureDetector(
        onTap: () => _handleJumpCategoryPage(context,banner.caseId),
        child: CachedNetworkImage(
          imageUrl: banner.bannerUrl ?? "",
          fit: BoxFit.cover,
        ));
  }

  Widget _buildCategorySection(
      BuildContext context, PhotoPortraitCategory? category) {
    if (category == null) {
      return const SizedBox.shrink();
    }

    final details = category.details ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category.caseName ?? "",
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
              GestureDetector(
                onTap: () => _handleJumpCategoryPage(context, category.id),
                child: const Row(
                  children: [
                    Text(
                      "更多",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 4),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 8,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 160,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: details.length,
            separatorBuilder: (context, index) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final detail = details[index];
              return _buildCategoryItem(
                detail,
                onTap: () => _handleCategoryItemTap(context, detail));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryItem(PhotoPortraitCategoryDetail detail,{Function? onTap}) {
    return GestureDetector(
      onTap: () => onTap?.call(),
      child: SizedBox(
        width: 110,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: CachedNetworkImage(
            imageUrl: detail.caseImage ?? "",
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey,
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey,
              child: const Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.white54,
                  size: 32,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理"更多"按钮点击事件
  void _handleJumpCategoryPage(BuildContext context, int? categoryId) {
    if (categoryId != null) {
      // 只传递 categoryId，让分类页面自己从 provider 获取数据
      context.push('/$photoPortraitCategoryPage', extra: {
        'categoryId': categoryId,
      });
    }
  }



  /// 处理分类项目点击事件
  void _handleCategoryItemTap(BuildContext context,PhotoPortraitCategoryDetail detail) {
    // TODO: 跳转到写真生成页面
    debugPrint("选择写真: ${detail.caseTitle}");
    context.push('/$photoPortraitDetailPage', extra: detail);
  }

  /// 构建Banner头部组件
  Widget _buildBannerHeader(List<PhotoPortraitBanner>? bannerList) {
    if (bannerList == null || bannerList.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Text(
            "暂无Banner数据",
            style: TextStyle(color: Colors.white60),
          ),
        ),
      );
    }
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 14),
      child: _buildHeader(bannerList),
    );
  }

  /// 构建分类列表组件
  Widget _buildCategoryList(List<PhotoPortraitCategory>? categoryList) {
    if (categoryList == null || categoryList.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Text(
            "暂无分类数据",
            style: TextStyle(color: Colors.white60),
          ),
        ),
      );
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: categoryList.length,
      itemBuilder: (context, index) {
        final category = categoryList[index];
        return _buildCategorySection(context, category);
      },
    );
  }

  /// 构建加载状态组件
  Widget _buildLoadingState() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(20.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// 构建错误状态组件
  Widget _buildErrorState(Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Text(
          "加载失败: $error",
          style: const TextStyle(color: Colors.red),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bannerAsync = ref.watch(fetchPhotoPortraitBannerProvider);
    final categoryAsync = ref.watch(fetchphotoPortraitCategoryListProvider);
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "真人写真",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {},
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Banner部分
            bannerAsync.when(
              data: (bannerList) => _buildBannerHeader(bannerList),
              loading: () => const SizedBox(
                height: 80,
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stackTrace) => const SizedBox(
                height: 80,
                child: Center(
                  child: Text(
                    "Banner加载失败",
                    style: TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ),
              ),
            ),
            // 分类列表部分
            categoryAsync.when(
              data: (categoryList) => _buildCategoryList(categoryList),
              loading: () => _buildLoadingState(),
              error: (error, stackTrace) => _buildErrorState(error),
            ),
          ],
        ),
      ),
    );
  }
}
