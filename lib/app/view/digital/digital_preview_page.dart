import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';

import '../../../config/icon_address.dart';
import '../../widgets/appbar/leading.dart';

class DigitalPreviewPage extends ConsumerStatefulWidget {
  const DigitalPreviewPage({
    super.key,
    required this.videoUrl,
  });

  final String? videoUrl;

  @override
  DigitalPreviewPageState createState() => DigitalPreviewPageState();
}

class DigitalPreviewPageState extends ConsumerState<DigitalPreviewPage> {
  late VideoPlayerController _controller;

  bool showPlay = false;

  @override
  void initState() {
    super.initState();
    debugPrint("creationResult: ${widget.videoUrl}");
    var videoUrl = widget.videoUrl ?? "";
    var uri = Uri.parse(videoUrl);
    _controller = VideoPlayerController.networkUrl(uri)
      ..initialize().then(
        (_) {
          // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
          debugPrint("initialize success------");
          setState(() {
            _controller.play();
            showPlay = false;
          });
        },
      ).catchError(
        (e) {
          debugPrint('catchError: $e');
        },
      );
    _controller.addListener(handle);
  }

  @override
  void dispose() {
    _controller.removeListener(handle);
    _controller.dispose();
    super.dispose();
  }

  void handle() {
    setState(() {});
  }

  Widget _buildVideo() {
    Widget child;
    debugPrint("isInitialized: ${_controller.value.isInitialized}");
    if (_controller.value.isInitialized) {
      debugPrint("isInitialized: ${_controller.value.aspectRatio}");
      var aspectRatio = _controller.value.aspectRatio;
      double width = MediaQuery.sizeOf(context).width;
      double height = width / aspectRatio;
      Widget pauseWidget = Container();
      if (!_controller.value.isPlaying) {
        pauseWidget = InkWell(
          onTap: () {
            setState(() {
              _controller.play();
              showPlay = false;
            });
          },
          child: Image.asset(
            pauseIcon,
            width: 60.w,
            height: 60.h,
            fit: BoxFit.contain,
          ),
        );
      }
      if (_controller.value.isPlaying && showPlay) {
        pauseWidget = InkWell(
          onTap: () {
            setState(() {
              _controller.pause();
            });
          },
          child: Image.asset(
            playIcon,
            width: 60.w,
            height: 60.h,
            fit: BoxFit.contain,
          ),
        );
      }
      child = Stack(
        alignment: Alignment.center,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                showPlay = true;
              });
            },
            child: SizedBox(
              width: width,
              height: height,
              child: VideoPlayer(_controller),
            ),
          ),
          pauseWidget,
        ],
      );
    } else {
      child = const Center(
        child: CircularProgressIndicator(
          color: Colors.green,
          strokeWidth: 1,
        ),
      );
    }
    return Container(
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          child,
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Column(
        children: [
          Expanded(child: Container(child: _buildVideo())),
        ],
      ),
    );
  }
}
