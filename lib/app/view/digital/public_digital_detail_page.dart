import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/repository/modals/digital/public_digital_human.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:video_player/video_player.dart';

import '../../../config/icon_address.dart';
import '../../provider/digital/digital_video_provider.dart';
import '../../widgets/appbar/leading.dart';

class PublicDigitalDetailPage extends ConsumerStatefulWidget {
  const PublicDigitalDetailPage({
    super.key,
    required this.publicDigitalHuman,
  });

  final PublicDigitalHuman publicDigitalHuman;

  @override
  PublicDigitalDetailPageState createState() => PublicDigitalDetailPageState();
}

class PublicDigitalDetailPageState
    extends ConsumerState<PublicDigitalDetailPage> {
  late VideoPlayerController _controller;

  bool showPlay = false;

  @override
  void initState() {
    super.initState();
    debugPrint("creationResult: ${widget.publicDigitalHuman.previewVideoUrl}");
    var videoUrl = widget.publicDigitalHuman.previewVideoUrl ?? "";
    var uri = Uri.parse(videoUrl);
    _controller = VideoPlayerController.networkUrl(uri)
      ..initialize().then(
        (_) {
          // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
          debugPrint("initialize success------");
          setState(() {});
        },
      ).catchError(
        (e) {
          debugPrint('catchError: $e');
        },
      );
    _controller.addListener(handle);
  }

  @override
  void dispose() {
    _controller.removeListener(handle);
    _controller.dispose();
    super.dispose();
  }

  void handle() {
    setState(() {});
  }

  String _personTypeText(String? type) {
    String text = "";
    if (type == "whole_body") {
      text = "全身";
    } else if (type == "circle_view") {
      text = "头像";
    } else if (type == "sit_body") {
      text = "坐姿";
    }
    return text;
  }

  Widget _buildVideo() {
    var url = widget.publicDigitalHuman.previewVideoUrl;
    if (url == null || url.isEmpty) {
      return CachedNetworkImage(
        imageUrl: widget.publicDigitalHuman.cover ?? "",
        errorWidget: (context, url, o) {
          return const SizedBox();
        },
      );
    }
    Widget child;
    debugPrint("isInitialized: ${_controller.value.isInitialized}");
    if (_controller.value.isInitialized) {
      debugPrint("isInitialized: ${_controller.value.aspectRatio}");
      var aspectRatio = _controller.value.aspectRatio;
      double width = 267.w;
      double height = width / aspectRatio;
      Widget pauseWidget = Container();
      if (!_controller.value.isPlaying) {
        pauseWidget = InkWell(
          onTap: () {
            setState(() {
              _controller.play();
              showPlay = false;
            });
          },
          child: Image.asset(
            pauseIcon,
            width: 60.w,
            height: 60.h,
            fit: BoxFit.contain,
          ),
        );
      }
      if (_controller.value.isPlaying && showPlay) {
        pauseWidget = InkWell(
          onTap: () {
            setState(() {
              _controller.pause();
            });
          },
          child: Image.asset(
            playIcon,
            width: 60.w,
            height: 60.h,
            fit: BoxFit.contain,
          ),
        );
      }
      child = Stack(
        alignment: Alignment.center,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                showPlay = true;
              });
            },
            child: SizedBox(
              width: width,
              height: height,
              child: VideoPlayer(_controller),
            ),
          ),
          pauseWidget,
        ],
      );
    } else {
      child = const Center(
        child: CircularProgressIndicator(
          color: Colors.green,
          strokeWidth: 1,
        ),
      );
    }
    return Container(
      width: 267.w,
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: child,
    );
  }

  Widget _buildDetail() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Text(
                "公共数字人：",
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF96969A),
                ),
              ),
              Text(
                "${widget.publicDigitalHuman.personName}",
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF141414),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              const Text(
                "性别：",
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF96969A),
                ),
              ),
              Text(
                "${widget.publicDigitalHuman.gender}",
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF141414),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              const Text(
                "形象：",
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF96969A),
                ),
              ),
              Text(
                _personTypeText(widget.publicDigitalHuman.personType),
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF141414),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              const Text(
                "形象特色：",
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF96969A),
                ),
              ),
              Text(
                "${widget.publicDigitalHuman.audioName}",
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF141414),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "定制数字人",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Column(
        children: [
          const SizedBox(height: 10),
          Expanded(child: Container(child: _buildVideo())),
          const SizedBox(height: 10),
          _buildDetail(),
          const SizedBox(height: 17),
          GradientButton(
            onPress: () {
              ref
                  .watch(publicSelectorDigitalProvider.notifier)
                  .setDigital(widget.publicDigitalHuman);
              context.push(
                "/$makeVideoDigitalPage",
                extra: widget.publicDigitalHuman,
              );
            },
            radius: 12,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 11),
            gradient: const LinearGradient(
              colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
            ),
            child: const Text(
              "创作同款数字人视频",
              style: TextStyle(
                fontSize: 18,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 14),
          const Text(
            "内容由AI生成，仅供参考",
            style: TextStyle(
              fontSize: 10,
              color: Color(0xFF96969A),
            ),
          ),
          SizedBox(
            height: MediaQuery.paddingOf(context).bottom +
                (MediaQuery.paddingOf(context).bottom > 0 ? 0 : 12),
          ),
        ],
      ),
    );
  }
}
