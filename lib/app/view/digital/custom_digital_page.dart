import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../widgets/appbar/leading.dart';

class CustomDigitalPage extends ConsumerStatefulWidget {
  const CustomDigitalPage({super.key});

  @override
  CustomDigitalPageState createState() => CustomDigitalPageState();
}

class CustomDigitalPageState extends ConsumerState<CustomDigitalPage> {
  final TextEditingController _textEditingController = TextEditingController();

  // 数字人名称
  Widget _buildInputWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          color: const Color(0xFFF9FAFC),
          borderRadius: BorderRadius.circular(8),
        ),
        child: TextField(
          decoration: const InputDecoration(
            hintText: "请输入数字人分身名称…",
            border: InputBorder.none,
            hintStyle: TextStyle(
              fontSize: 14,
              color: Color(0xFF868688),
            ),
          ),
          maxLines: 1,
          maxLength: 32,
          buildCounter: (
            BuildContext context, {
            required int currentLength,
            required bool isFocused,
            required int? maxLength,
          }) {
            return null; // ✅ 返回 null 即可隐藏计数器
          },
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black,
          ),
          controller: _textEditingController,
        ),
      ),
    );
  }

  // 上传区
  Widget _buildUploadWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 68, bottom: 51),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () {},
                  child: Column(
                    children: [
                      Image.asset(uploadVideoIcon, width: 26, height: 23),
                      const SizedBox(height: 5),
                      const Text(
                        "上传视频",
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF141414),
                        ),
                      ),
                    ],
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 26),
                  child: Text(
                    "或",
                    style: TextStyle(
                      fontSize: 18,
                      color: Color(0xFF747474),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    context.push("/$makeVideoPage");
                  },
                  child: Column(
                    children: [
                      Image.asset(uploadVideoIcon, width: 26, height: 19),
                      const SizedBox(height: 7),
                      const Text(
                        "拍摄视频",
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF141414),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const Column(
            children: [
              Text(
                "请上传或拍摄单人视频",
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                  color: Color(0xFF838383),
                ),
              ),
              Text(
                "注意每一帧都要有露脸,侧脸幅度不可过大",
                style: TextStyle(
                  fontSize: 10,
                  color: Color(0xFF838383),
                ),
              ),
              Text(
                "(可上传无声视频,如需克隆视频里的声音则需上传有声视频)",
                style: TextStyle(
                  fontSize: 10,
                  color: Color(0xFF838383),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // 视频要求说明
  Widget _buildDesc() {
    var crossAxisCount = 2;
    var itemWidth = (MediaQuery.sizeOf(context).width - 32) / crossAxisCount;
    var data = {
      "视频方向：": "横向或纵向",
      "分辨率：": "360p~4K",
      "文件格式：": "mp4,mov,WebM",
      "文件大小：": "小于2G",
      "视频时长：": "30秒-5分钟"
    };
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "视频要求:",
            style: TextStyle(
              fontSize: 15,
              color: Color(0xFF141414),
            ),
          ),
          const SizedBox(height: 12),
          GridView(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: itemWidth / 17,
            ),
            children: data.entries.map((entry) {
              return RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 12),
                  children: [
                    TextSpan(
                      text: entry.key,
                      style: const TextStyle(color: Color(0xFF96969A)),
                    ),
                    TextSpan(
                      text: entry.value,
                      style: const TextStyle(color: Color(0xFF141414)),
                    )
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // 是否同时提取声音
  Widget _buildBothVoice() {
    bool check = true;
    Widget child;
    BoxDecoration decoration;
    if (check) {
      decoration = BoxDecoration(
        color: const Color(0xFF00D873),
        borderRadius: BorderRadius.circular(8),
      );
      child = const Icon(
        Icons.check,
        size: 12,
        color: Colors.white,
      );
    }
    // else {
    //   decoration = BoxDecoration(
    //     border: Border.all(
    //       color: const Color(0xFFA9A9A9),
    //       width: 1,
    //     ),
    //     borderRadius: BorderRadius.circular(8),
    //   );
    //   child = const SizedBox();
    // }
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 15,
          height: 15,
          decoration: decoration,
          child: child,
        ),
        const SizedBox(width: 8),
        const Text(
          "同时提取视频中的声音克隆数字人声音",
          style: TextStyle(
            fontSize: 12,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "定制数字人",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Column(
        children: [
          const SizedBox(height: 28),
          const Padding(
            padding: EdgeInsets.only(left: 16),
            child: Row(
              children: [
                Text(
                  "数字人名称",
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF141414),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          _buildInputWidget(),
          const SizedBox(height: 10),
          _buildUploadWidget(),
          const SizedBox(height: 12),
          _buildDesc(),
          const Expanded(child: SizedBox()),
          _buildBothVoice(),
          const SizedBox(height: 12),
          GradientButton(
            onPress: () {},
            radius: 12,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 11),
            gradient: const LinearGradient(
              colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
            ),
            child: const Text(
              "提交定制数字人",
              style: TextStyle(
                fontSize: 18,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: MediaQuery.paddingOf(context).bottom),
        ],
      ),
    );
  }
}
