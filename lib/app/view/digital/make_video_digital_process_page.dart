import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../config/icon_address.dart';
import '../../provider/home/<USER>';
import '../../widgets/appbar/leading.dart';

class MakeVideoDigitalProcessPage extends ConsumerWidget {
  const MakeVideoDigitalProcessPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "定制数字人视频",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: Column(
        children: [
          Column(
            children: [
              Padding(padding: EdgeInsets.only(top: 68.h)),
              Image.asset(
                creationResult,
                width: 166.w,
                height: 166.h,
              ),
              Text(
                "视频已提交制作\n请耐心等待视频生成",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: const Color(0xFF2D2D2D),
                ),
              ),
              SizedBox(
                height: 42.h,
              ),
              Text(
                "（生成进度及结果可前往“我的”页查看）",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF747474),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 38.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GradientButton(
                onPress: () {
                  context.go("/");
                  ref.read(homeProvider.notifier).jumpToPage(2);
                },
                radius: 8.r,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 51),
                gradient: const LinearGradient(
                  colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                ),
                child: Text(
                  "去查看",
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const Expanded(child: SizedBox()),
          SizedBox(height: 14.h),
          const Text(
            "内容由AI生成，仅供参考",
            style: TextStyle(
              fontSize: 10,
              color: Color(0xFF96969A),
            ),
          ),
          SizedBox(height: MediaQuery.paddingOf(context).bottom),
        ],
      ),
    );
  }
}
