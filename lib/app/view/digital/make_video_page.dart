import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../widgets/appbar/leading.dart';

class MakeVideoPage extends ConsumerStatefulWidget {
  const MakeVideoPage({super.key});

  @override
  MakeVideoPageState createState() => MakeVideoPageState();
}

class MakeVideoPageState extends ConsumerState<MakeVideoPage>
    with WidgetsBindingObserver {
  late CameraController _controller;
  CameraDescription? _frontCamera;
  CameraDescription? _backCamera;
  bool _isCameraInitialized = false;

  @override
  void initState() {
    super.initState();
    initCamera();
  }

  // 初始化摄像头
  Future<void> initCamera() async {
    final cameras = await availableCameras();
    if (cameras.isEmpty) {
      ToastUtil.showToast("暂无可用摄像头");
      return;
    }
    _frontCamera = cameras.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.front,
    );

    _backCamera = cameras.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.back,
    );

    if (_frontCamera == null && _backCamera == null) {
      ToastUtil.showToast("暂无可用摄像头");
      return;
    }

    // 设置参数
    optionsCamera(_frontCamera ?? _backCamera!);
  }

  // 摄像头参数
  Future<void> optionsCamera(CameraDescription cameraDescription) async {
    _controller = CameraController(
      cameraDescription,
      ResolutionPreset.high,
      enableAudio: true,
    );

    await _controller.initialize();
    setState(() {
      _isCameraInitialized = true;
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final CameraController cameraController = _controller;

    // App state changed before we got the chance to initialize.
    if (!cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      optionsCamera(cameraController.description);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "创造数字人",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
      ),
      body: _isCameraInitialized
          ? Column(
              children: [
                Expanded(
                  child: CameraPreview(
                    _controller,
                    child: Column(
                      children: [
                        const SizedBox(height: 107),
                        Image.asset(faceVideoIcon, width: 279.w),
                        const SizedBox(height: 39),
                        GradientButton(
                          onPress: () {},
                          radius: 12,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          margin: const EdgeInsets.symmetric(horizontal: 50),
                          gradient: const LinearGradient(
                            colors: [Color(0xFF4AEFAD), Color(0xFF05D676)],
                          ),
                          child: const Text(
                            "开始录制",
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF141414),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )
          : const Center(
              child: CircularProgressIndicator(),
            ),
    );
  }
}
