import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/navigation/text_to_video.dart';

import 'config/global_config.dart';
import 'utils/platform_util.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  /// 全局配置信息
  await GlobalConfig.init();

  /// 强制竖屏
  SystemChrome.setPreferredOrientations(
    [
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ],
  );

  /// 状态拦透明
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarContrastEnforced: false,
    ),
  );

  SystemChrome.setEnabledSystemUIMode(
    /// 配合 SystemUiOverlayStyle 的 systemNavigationBarColor: Colors.transparent，可达到底部系统导航透明效果；
    /// 如果系统导航是3按钮导航，那么可以设置 systemNavigationBarContrastEnforced： false，取消默认的半透明效果。
    SystemUiMode.edgeToEdge,
  );

  if (kReleaseMode) {
    debugPrint = (String? message, {int? wrapWidth}) {
      // empty debugPrint implementation in the release mode
    };
  }

  if (PlatformUtils.isAndroid) {
    await InAppWebViewController.setWebContentsDebuggingEnabled(true);

    var swAvailable = await WebViewFeature.isFeatureSupported(
      WebViewFeature.SERVICE_WORKER_BASIC_USAGE,
    );
    var swInterceptAvailable = await WebViewFeature.isFeatureSupported(
      WebViewFeature.SERVICE_WORKER_SHOULD_INTERCEPT_REQUEST,
    );

    if (swAvailable && swInterceptAvailable) {
      ServiceWorkerController serviceWorkerController =
          ServiceWorkerController.instance();

      await serviceWorkerController.setServiceWorkerClient(
        ServiceWorkerClient(
          shouldInterceptRequest: (request) async {
            return null;
          },
        ),
      );
    }
  }

  /// 启动 app
  runApp(
    const ProviderScope(
      child: TextToVideo(),
    ),
  );
}
