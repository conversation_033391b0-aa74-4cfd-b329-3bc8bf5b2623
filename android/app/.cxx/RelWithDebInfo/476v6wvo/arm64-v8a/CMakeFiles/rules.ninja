# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.18

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: Project
# Configurations: RelWithDebInfo
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/cmake --regenerate-during-build -S/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy -B/Users/<USER>/project/texttovideo/android/app/.cxx/RelWithDebInfo/476v6wvo/arm64-v8a
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja -t targets
  description = All primary targets available:

